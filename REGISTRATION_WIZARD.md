# 🧙‍♂️ Мастер многошаговой регистрации

## 📋 Обзор

Реализована многошаговая форма регистрации пользователей с адаптивным дизайном и прогресс-баром. Форма автоматически адаптируется в зависимости от выбранной правовой формы (физическое/юридическое лицо).

## 🎯 Компоненты

### 1. `RegistrationWizard.jsx` - Основной компонент

- Управляет состоянием формы и навигацией между шагами
- Содержит первые 4 шага (общие для всех типов пользователей)
- Динамически определяет количество шагов в зависимости от правовой формы

### 2. `RegistrationWizardSteps.jsx` - Дополнительные шаги

- Содержит шаги 6-9 (специфичные для юридических лиц)
- Виды деятельности, портфолио, сертификаты, рассказ о компании

### 3. `UserProfileDisplay.jsx` - Отображение профиля

- Красивое отображение заполненного профиля пользователя
- Адаптивный дизайн с иконками и тегами
- Кнопки редактирования и выхода

## 🚀 Шаги регистрации

### Для всех пользователей (4 шага):

1. **Правовая форма** - Выбор физ/юр лица
2. **Данные пользователя** - ФИО, телефон, email
3. **Город** - Выбор из списка городов
4. **Фото профиля** - Загрузка изображения (опционально)

### Дополнительно для юридических лиц (+5 шагов):

5. **Данные компании** - Название, БИН, адреса, контакты
6. **Виды деятельности** - Выбор специализаций с поиском
7. **Портфолио** - Добавление работ с описанием и фото
8. **Сертификаты** - Загрузка документов и лицензий
9. **О компании** - Свободный рассказ о деятельности

## 🎨 Дизайн-система

### Цвета:

- **Основной**: `#0066cc` (синий)
- **Успех**: `#28a745` (зеленый)
- **Фон**: `#f8f9fa` (светло-серый)
- **Границы**: `#e9ecef` (серый)

### Компоненты:

- **Прогресс-бар** с процентами завершения
- **Кнопки "Назад"** с иконкой стрелки на каждом шаге (кроме первого)
- **Радио-кнопки** с кастомным дизайном
- **Поля ввода** с фокус-эффектами
- **Кнопки** с hover-анимациями
- **Загрузка файлов** drag-and-drop зоны

## 🔧 Интеграция

### В страницу авторизации (`src/app/auth/page.js`):

```javascript
import RegistrationWizard from "../../components/RegistrationWizard";

// После успешной базовой регистрации
const success = await register(email, password, confirmPassword, rememberMe);
if (success) {
  setShowRegistrationWizard(true); // Показать мастер
}

// Обработчики
const handleRegistrationComplete = (profileData) => {
  console.log("Профиль заполнен:", profileData);
  // API запрос для сохранения данных
  setShowRegistrationWizard(false);
};

// Рендер мастера
if (showRegistrationWizard) {
  return (
    <RegistrationWizard
      initialEmail={formData.email}
      onComplete={handleRegistrationComplete}
      onCancel={handleRegistrationCancel}
    />
  );
}
```

## 📱 Адаптивность

- **Desktop**: Полная ширина с максимумом 500px
- **Tablet**: Адаптивные отступы и размеры
- **Mobile**: Вертикальная компоновка, увеличенные кнопки

## 🧪 Тестирование

Создана тестовая страница: `/registration-test`

```bash
# Запуск проекта
npm run dev

# Переход на тестовую страницу
http://localhost:3000/registration-test
```

## 📊 Структура данных

```javascript
const profileData = {
  // Базовые данные
  email: "<EMAIL>",
  legalForm: "individual" | "legal",
  firstName: "Иван",
  lastName: "Иванов",
  middleName: "Иванович",
  phone: "+7 (777) 123-45-67",
  city: "Алматы",
  profileImage: null,

  // Для юридических лиц
  companyName: "ООО Компания",
  bin: "123456789012",
  legalAddress: "Адрес...",
  factualAddress: "Адрес...",
  sameAddress: true,
  contactPhone: "+7 (777) 987-65-43",

  // Деятельность и портфолио
  activities: ["Электрика", "Сантехника"],
  portfolio: [
    {
      name: "Проект 1",
      description: "Описание...",
      images: [],
    },
  ],
  certificates: [
    {
      name: "Сертификат",
      description: "Описание...",
      images: [],
    },
  ],
  companyStory: "Рассказ о компании...",
};
```

## 🔮 Будущие улучшения

1. **API интеграция** - Подключение к бэкенду для сохранения данных
2. **Валидация** - Расширенная валидация полей
3. **Загрузка файлов** - Реальная загрузка изображений
4. **Автосохранение** - Сохранение прогресса в localStorage
5. **Анимации** - Плавные переходы между шагами
6. **Локализация** - Поддержка нескольких языков

## ✅ Последние обновления

### Добавлены кнопки "Назад":

- Красивая кнопка с иконкой стрелки на каждом шаге (кроме первого)
- Стиль: светло-серый фон с синей иконкой и текстом
- Hover-эффект с изменением цвета границы
- Автоматическая передача функции `onBack` во все шаги

### Исправлено дублирование прогресса:

- Убран дублирующийся текст "Подробная информация о компании..."
- Каждый шаг теперь имеет уникальный заголовок
- Прогресс-бар отображается только в основном контейнере

### Улучшена навигация:

- Кнопки "Назад" встроены в каждый шаг
- Убраны дублирующиеся кнопки навигации снизу
- Сохранена кнопка "Отмена" только на первом шаге

## 🎉 Готово к использованию!

Многошаговая форма регистрации полностью готова к интеграции в проект. Все компоненты созданы с учетом существующей дизайн-системы и styled-components архитектуры проекта.
