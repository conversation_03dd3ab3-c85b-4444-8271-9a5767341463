import { useQuery } from "@tanstack/react-query";
import ApiService from "../services/api.service";
import API_CONFIG from "../config/api";

/**
 * Хук для получения каталога
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useCatalog = (options = {}) => {
  return useQuery({
    queryKey: ["catalog"],
    queryFn: () => ApiService.getCatalog(),
    ...options,
  });
};

/**
 * Хук для получения детальной информации о продукте
 * @param {string} productId - ID продукта
 * @param {Object} options - Опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useProductDetails = (productId, options = {}) => {
  return useQuery({
    queryKey: ["productDetails", productId],
    queryFn: () => ApiService.getProductById(productId),
    // Не выполнять запрос, если ID продукта не указан
    enabled: !!productId,
    ...options,
  });
};

/**
 * Хук для поиска товаров
 * @param {string} searchQuery - Поисковый запрос
 * @param {Object} options - Дополнительные опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useSearchProducts = (searchQuery, options = {}) => {
  return useQuery({
    queryKey: ["searchProducts", searchQuery],
    queryFn: async () => {
      if (!searchQuery || searchQuery.length < 2) {
        return [];
      }

      try {
        // Прямой запрос к API с параметром name для поиска
        const encodedQuery = encodeURIComponent(searchQuery);
        const url = `${API_CONFIG.BASE_URL}/api/Materials/?name=${encodedQuery}`;

        console.log("Поиск товаров:", url);

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Возвращаем массив товаров
        return data.results || data.data || data || [];
      } catch (error) {
        console.error("Ошибка при поиске товаров:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 минут
    cacheTime: 10 * 60 * 1000, // 10 минут
    enabled: searchQuery && searchQuery.length >= 2,
    ...options,
  });
};

/**
 * Хук для получения товаров с пагинацией
 * @param {number} page - Номер страницы
 * @param {number} pageSize - Размер страницы
 * @param {Object} filters - Фильтры поиска
 * @param {Object} options - Дополнительные опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useProducts = (
  page = 1,
  pageSize = 20,
  filters = {},
  options = {}
) => {
  return useQuery({
    queryKey: ["products", page, pageSize, filters],
    queryFn: async () => {
      try {
        return await ApiService.getProductsWithPagination(
          page,
          pageSize,
          filters
        );
      } catch (error) {
        console.error("Ошибка при получении товаров:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 минут
    cacheTime: 10 * 60 * 1000, // 10 минут
    keepPreviousData: true, // Сохраняем предыдущие данные при смене страницы
    ...options,
  });
};

/**
 * Хук для получения поставщиков товара
 * @param {number} materialId - ID материала
 * @param {Object} options - Дополнительные опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useSuppliers = (materialId, options = {}) => {
  return useQuery({
    queryKey: ["suppliers", materialId],
    queryFn: async () => {
      try {
        if (!materialId) {
          return [];
        }

        return await ApiService.getSuppliers(materialId);
      } catch (error) {
        console.error("Ошибка при получении поставщиков:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 минут
    cacheTime: 10 * 60 * 1000, // 10 минут
    enabled: !!materialId,
    ...options,
  });
};

/**
 * Хук для получения средних цен товаров
 * @param {Array} materialIds - Массив ID материалов
 * @param {Object} options - Дополнительные опции для useQuery
 * @returns {Object} - Результат запроса
 */
export const useAveragePrices = (materialIds, options = {}) => {
  return useQuery({
    queryKey: ["averagePrices", materialIds],
    queryFn: async () => {
      try {
        if (!materialIds || materialIds.length === 0) {
          return [];
        }

        return await ApiService.getAveragePrices(materialIds);
      } catch (error) {
        console.error("Ошибка при получении средних цен:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 минут
    cacheTime: 30 * 60 * 1000, // 30 минут
    enabled: materialIds && materialIds.length > 0,
    ...options,
  });
};
