"use client";

import { useState, useEffect } from "react";
import { useAveragePricesContext } from "../context/AveragePricesContext";

/**
 * Хук для загрузки изображения товара (новый API с автоматическим переключением)
 * @param {number|string} materialId - ID товара
 * @returns {Object} - Объект с URL изображения и состоянием загрузки
 */
export const useProductImage = (materialId) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  const {
    getImageUrlsByMaterialId,
    getFirstImageUrlByMaterialId,
    filesLoading,
  } = useAveragePricesContext();

  // Получаем все URL изображений для данного материала
  const imageUrls = getImageUrlsByMaterialId(materialId);
  const hasMultipleImages = imageUrls.length > 1;

  // Автоматическая карусель - мгновенное переключение каждые 2 секунды
  useEffect(() => {
    if (!hasMultipleImages) return;

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % imageUrls.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [hasMultipleImages, imageUrls.length]);

  // Сброс индекса при изменении materialId
  useEffect(() => {
    setCurrentImageIndex(0);
    setIsLoading(filesLoading);
    setHasError(false);
  }, [materialId, filesLoading]);

  const currentImageUrl = hasMultipleImages
    ? imageUrls[currentImageIndex]
    : getFirstImageUrlByMaterialId(materialId);

  return {
    imageUrl: currentImageUrl,
    isLoading,
    hasError,
  };
};

/**
 * Хук для загрузки всех изображений товара (новый API)
 * @param {number|string} materialId - ID товара
 * @returns {Object} - Объект с массивом изображений и состоянием загрузки
 */
export const useProductImages = (materialId) => {
  const [images, setImages] = useState([
    {
      thumbnail: "/images/placeholder.png",
      full: "/images/placeholder.png",
      alt: "Изображение товара",
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  const { getImageUrlsByMaterialId, filesLoading } = useAveragePricesContext();

  useEffect(() => {
    if (!materialId) {
      setImages([
        {
          thumbnail: "/images/placeholder.png",
          full: "/images/placeholder.png",
          alt: "Изображение товара",
        },
      ]);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    setIsLoading(filesLoading);

    // Получаем все URL изображений из контекста
    const imageUrls = getImageUrlsByMaterialId(materialId);

    // Преобразуем URL в формат для галереи
    const imageObjects = imageUrls.map((url, index) => ({
      thumbnail: url,
      full: url,
      alt: `Изображение товара ${index + 1}`,
    }));

    setImages(imageObjects);
    setHasError(false);
  }, [materialId, getImageUrlsByMaterialId, filesLoading]);

  return {
    images,
    isLoading,
    hasError,
  };
};
