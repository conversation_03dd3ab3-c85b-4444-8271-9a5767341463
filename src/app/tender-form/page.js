import { Suspense } from "react";
import Layout from "../../components/Layout";
import TenderFormClient from "./TenderFormClient";
import { ISRUtils } from "../../config/isr";

// Генерация мета-тегов для SEO
export async function generateMetadata() {
  return {
    title: "Форма создания тендера - Строительный маркетплейс",
    description:
      "Заполните форму создания тендера: укажите количество, цены, сроки поставки и требования к материалам. Опубликуйте тендер для поставщиков.",
    keywords: [
      "форма тендера",
      "создание тендера",
      "заполнить тендер",
      "тендер форма",
      "закуп материалов",
      "тендерная документация",
      "требования к поставке",
      "сроки поставки",
      "цены материалов",
      "количество материалов",
      "создать тендер онлайн",
      "тендер строительные материалы",
      "заявка на поставку",
      "коммерческое предложение",
      "оптовая закупка",
      "закупочная процедура",
      "тендер Казахстан",
      "строительный тендер",
    ],
    openGraph: {
      title: "Форма создания тендера",
      description:
        "Заполните форму создания тендера: укажите количество, цены, сроки поставки и требования к материалам.",
      type: "website",
      url: "https://shop.sadi.kz/tender-form",
      siteName: "Строительный маркетплейс",
      locale: "ru_RU",
      images: [
        {
          url: "/images/placeholder.png",
          width: 1200,
          height: 630,
          alt: "Форма создания тендера",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Форма создания тендера",
      description:
        "Заполните форму создания тендера: укажите количество, цены, сроки поставки и требования к материалам.",
      images: ["/images/placeholder.png"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: "https://shop.sadi.kz/tender-form",
    },
  };
}

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Форма создания тендера",
  description:
    "Заполните форму создания тендера на закуп строительных материалов",
  url: "https://shop.sadi.kz/tender-form",
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Создание тендера",
        item: "https://shop.sadi.kz/create-tender",
      },
      {
        "@type": "ListItem",
        position: 3,
        name: "Форма тендера",
        item: "https://shop.sadi.kz/tender-form",
      },
    ],
  },
};

// Компонент загрузки
function TenderFormLoading() {
  return (
    <div
      style={{
        padding: "40px 20px",
        textAlign: "center",
        minHeight: "400px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div>
        <h1>Создание тендера</h1>
        <p>Загрузка формы...</p>
        <div
          style={{
            width: "40px",
            height: "40px",
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #0066cc",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "20px auto",
          }}
        />
      </div>
    </div>
  );
}

// ISR: Кэширование формы тендера на 14 дней
export const revalidate = 1209600; // 14 дней в секундах

// Основной серверный компонент
export default function TenderFormPage() {
  // Логируем ISR операцию
  ISRUtils.logISROperation("tender-form-page", {
    revalidate: 1209600,
    note: "Форма тендера с ISR кэшированием",
  });
  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO-контент для поисковых систем */}
      <div style={{ display: "none" }}>
        <h1>Форма создания тендера на закуп материалов</h1>
        <h2>Заполнение тендерной документации</h2>
        <p>
          На этой странице вы можете заполнить подробную информацию о тендере:
          указать количество необходимых материалов, максимальные цены, сроки
          поставки, адрес доставки и дополнительные требования.
        </p>
        <h3>Что нужно указать в форме тендера</h3>
        <ul>
          <li>Количество каждого материала</li>
          <li>Максимальную цену за единицу</li>
          <li>Сроки поставки</li>
          <li>Адрес доставки</li>
          <li>Дополнительные требования</li>
        </ul>
      </div>

      {/* Клиентский компонент с Suspense */}
      <Suspense fallback={<TenderFormLoading />}>
        <TenderFormClient />
      </Suspense>
    </Layout>
  );
}
