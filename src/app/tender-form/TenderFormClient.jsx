"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "../../context/AuthContext";
import authService from "../../services/auth.service";
import API_CONFIG from "../../config/api";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderFormContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
  margin-bottom: 24px;
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 12px 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;
ProductHeader.displayName = "ProductHeader";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const RecommendedText = styled.div`
  font-size: 12px;
  color: #656d78;
  margin-top: 4px;
  font-weight: 400;
`;
RecommendedText.displayName = "RecommendedText";

const ClearAllButtonContainer = styled.div`
  display: flex;
  align-items: flex-start;
`;
ClearAllButtonContainer.displayName = "ClearAllButtonContainer";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 16px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #666;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5c6cb;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const DeliverySection = styled.div`
  margin-bottom: 32px;
`;
DeliverySection.displayName = "DeliverySection";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const DeliveryText = styled.p`
  font-size: 14px;
  color: #969ea7;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
DeliveryText.displayName = "DeliveryText";

const DatePickerContainer = styled.div`
  background: #f5f5f5;
  padding: 48px 0;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
DatePickerContainer.displayName = "DatePickerContainer";

const AddressContainer = styled.div`
  margin-bottom: 24px;
`;
AddressContainer.displayName = "AddressContainer";

const AddressRow = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;
AddressRow.displayName = "AddressRow";

const AddressInput = styled(Input)`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  padding-right: 16px;
`;
AddressInput.displayName = "AddressInput";

const CityInput = styled(AddressInput)`
  max-width: 200px;
`;
CityInput.displayName = "CityInput";

const CityDropdown = styled.div`
  position: relative;
  width: 300px;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #434a54;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }

  &:hover {
    border-color: #0066cc;
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #434a54;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#ddd")};
  border-radius: 4px;
  background-color: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
    opacity: ${(props) => (props.checked ? 1 : 0)};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

const PublishButton = styled(CreateTenderButton)`
  font-size: 16px;
  padding: 16px 32px;
  margin-top: 32px;
`;
PublishButton.displayName = "PublishButton";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }

  /* 📱 Медиа-запросы для адаптива */
  @media (max-width: 768px) {
    .react-datepicker {
      padding: 16px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .react-datepicker__current-month {
      font-size: 16px;
    }

    .react-datepicker__navigation {
      top: 24px;
      width: 22px;
      height: 22px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .react-datepicker {
      padding: 12px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .react-datepicker__current-month {
      font-size: 14px;
    }

    .react-datepicker__navigation {
      top: 20px;
      width: 20px;
      height: 20px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 12px;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

const TenderNameContainer = styled.div`
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  gap: 16px;
`;
TenderNameContainer.displayName = "TenderNameContainer";

const TenderInputContainer = styled.div`
  position: relative;
  flex: 1;
`;
TenderInputContainer.displayName = "TenderInputContainer";

const TenderNameInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNameInput.displayName = "TenderNameInput";

const TenderNumberInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNumberInput.displayName = "TenderNumberInput";

const NoProductsMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoProductsMessage.displayName = "NoProductsMessage";

// Стили для уведомления об успешном создании тендера
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  max-width: 90%;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

const TenderFormClient = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderName, setTenderName] = useState("");
  const [tenderNumber, setTenderNumber] = useState("");
  const [deliveryDate, setDeliveryDate] = useState(null); // Для DatePicker используем null
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [city, setCity] = useState("Алматы");
  const [selectedCityId, setSelectedCityId] = useState("02"); // По умолчанию Алматы
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [deliveryPriceIncluded, setDeliveryPriceIncluded] = useState(true); // true - с доставкой, false - без доставки
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [createdTenderDocNum, setCreatedTenderDocNum] = useState("");

  // Список городов (в будущем будет загружаться из API)
  const cities = [
    {
      RegionId: "01",
      RegionName: "Нур-Султан",
      LivingWage: 22702,
      Coefficient: 1.155,
    },
    {
      RegionId: "02",
      RegionName: "Алматы",
      LivingWage: 22283,
      Coefficient: 1.134,
    },
    {
      RegionId: "04",
      RegionName: "Актюбинская область",
      LivingWage: 18010,
      Coefficient: 0.917,
    },
    {
      RegionId: "05",
      RegionName: "Алматинская область",
      LivingWage: 20557,
      Coefficient: 1.046,
    },
    {
      RegionId: "06",
      RegionName: "Атырауская область",
      LivingWage: 20297,
      Coefficient: 1.033,
    },
    {
      RegionId: "07",
      RegionName: "Западно-Казахстанская область",
      LivingWage: 17947,
      Coefficient: 0.913,
    },
    {
      RegionId: "08",
      RegionName: "Жамбылская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "09",
      RegionName: "Карагандинская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "10",
      RegionName: "Костанайская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "11",
      RegionName: "Кызылординская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "12",
      RegionName: "Мангистауская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "13",
      RegionName: "Туркестанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "14",
      RegionName: "Павлодарская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "15",
      RegionName: "Северо-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "16",
      RegionName: "Восточно-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "17",
      RegionName: "Акмолинская область",
      LivingWage: 18246,
      Coefficient: 0.929,
    },
    {
      RegionId: "3 ",
      RegionName: "Шымкент",
      LivingWage: 20283,
      Coefficient: 1,
    },
  ];

  // Получаем товары из localStorage
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [formData, setFormData] = useState([]);
  const [averagePrices, setAveragePrices] = useState([]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem("selectedTenderProducts");
        const products = saved ? JSON.parse(saved) : [];
        setSelectedProducts(products);

        // Загружаем рекомендуемые цены
        const savedPrices = localStorage.getItem("tenderAveragePrices");
        const prices = savedPrices ? JSON.parse(savedPrices) : [];
        setAveragePrices(prices);

        // Инициализируем форму для каждого товара
        setFormData(
          products.map((product) => {
            // Ищем рекомендуемую цену для этого товара
            const priceData = prices.find(
              (price) => price.MaterialId === product.MaterialId
            );
            const recommendedPrice = priceData ? priceData.AvgRetailPrice : "";

            return {
              productId: product.MaterialId,
              productName: product.MaterialName,
              productCode: product.Code,
              productUnit: product.UnitId,
              quantity: "",

              maxPrice: recommendedPrice, // Автоматически заполняем рекомендуемой ценой
              description: "",
              analogAllowed: true,
              attachedFiles: [],
            };
          })
        );
      } catch (error) {
        console.error("Ошибка при загрузке данных из localStorage:", error);
      }
    }
  }, []);

  // Закрытие dropdown при клике вне его
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isCityDropdownOpen && !event.target.closest("[data-city-dropdown]")) {
        setIsCityDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCityDropdownOpen]);

  const handleFormDataChange = (index, field, value) => {
    setFormData((prev) => {
      const newData = [...prev];
      newData[index] = { ...newData[index], [field]: value };
      return newData;
    });
  };

  // Функция для получения рекомендуемой цены для товара
  const getRecommendedPrice = (productId) => {
    const priceData = averagePrices.find(
      (price) => price.MaterialId === productId
    );
    return priceData ? priceData.AvgRetailPrice : null;
  };

  // Функции для работы с dropdown городов
  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleCitySelect = (selectedCity) => {
    setCity(selectedCity.RegionName);
    setSelectedCityId(selectedCity.RegionId);
    setIsCityDropdownOpen(false);
  };

  // Получаем текущий выбранный город
  const getCurrentCity = () => {
    return cities.find((city) => city.RegionId === selectedCityId) || cities[1]; // По умолчанию Алматы
  };

  // Функция для закрытия уведомления об успешном создании
  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  // Функция для получения нового PurchReqId
  const getPurchReqId = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/Gen_Id`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const purchReqId = await response.json();
        console.log("✅ Получен PurchReqId:", purchReqId);
        return purchReqId;
      } else {
        console.error("❌ Ошибка получения PurchReqId:", response.status);
        throw new Error(`Ошибка получения ID: ${response.status}`);
      }
    } catch (error) {
      console.error("❌ Ошибка при запросе PurchReqId:", error);
      throw error;
    }
  };

  // Функция для создания заголовка тендера
  const createTenderHeader = async (purchReqId, docNum) => {
    try {
      const authHeaders = await authService.getAuthHeaders();

      // Форматируем даты правильно
      const currentDate = new Date();
      const endDate = deliveryDate
        ? new Date(deliveryDate)
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // +30 дней по умолчанию

      const tenderHeaderData = {
        PurchReqId: purchReqId,
        IdLocalQuoteOfPurchaseRequest: 0,
        DocNum: docNum,
        PurchReqName: tenderName.trim(),
        CompanyId: 0,
        UserId: user?.userId || "00000000-0000-0000-0000-000000000000",
        CompanyId: user?.companyId || "0000",
        CreateDateTime: currentDate.toISOString(),
        PurchBegDate: currentDate.toISOString(),
        PurchEndDate: endDate.toISOString(),
        IsPublish: false,
        RegionId: selectedCityId.trim(),
        PurchStatus: 1,
        DocElementId: 0,
        DocStateId: 0,
        DeliveryAddress: deliveryAddress.trim(),
        IsSpecification: false,
        Description: "",
        CategoryId: 0,
      };

      console.log("📤 Отправка заголовка тендера:", tenderHeaderData);
      console.log("🔑 Заголовки авторизации:", authHeaders);

      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...authHeaders,
          },
          body: JSON.stringify(tenderHeaderData),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Заголовок тендера создан:", result);
        return result;
      } else {
        const errorText = await response.text();
        console.error(
          "❌ Ошибка создания заголовка тендера:",
          response.status,
          errorText
        );
        console.error("❌ Данные заголовка:", tenderHeaderData);
        throw new Error(
          `Ошибка создания заголовка тендера: ${response.status} - ${errorText}`
        );
      }
    } catch (error) {
      console.error("❌ Ошибка при создании заголовка тендера:", error);
      throw error;
    }
  };

  // Функция для создания строк материалов тендера (каждый материал отдельным запросом)
  const createTenderLines = async (purchReqId) => {
    try {
      const authHeaders = await authService.getAuthHeaders();
      const results = [];

      console.log(
        `🔄 Обрабатываем ${formData.length} материалов с PurchReqId: ${purchReqId}`
      );

      // Отправляем каждый материал отдельным запросом с одним и тем же PurchReqId
      for (let i = 0; i < formData.length; i++) {
        const product = formData[i];

        console.log(
          `📦 Материал ${i + 1}/${formData.length}: ${product.productName}`
        );

        const tenderLine = {
          PurchReqLineId: 0,
          PurchReqId: purchReqId, // Один и тот же PurchReqId для всех материалов
          ListBookId: 0,
          MaterialId: product.productId,
          Code:
            selectedProducts.find((p) => p.MaterialId === product.productId)
              ?.MaterialCode || "",
          MaterialName: product.productName,
          PurchQty: parseFloat(product.quantity) || 0,
          PurchUnit: product.productUnit,
          Code: product.productCode,
          PurchOpenPrice: parseFloat(product.maxPrice) || 0,
          RegionId: selectedCityId,
          TermDelivery: 0,
          DescriptionDelivery: "",
          Description: product.description || "",
          ReleasePrice: 0,
          CalculatePrice: 0,
          PurchStatusLineId: 1,
          OriginalOnly: !product.analogAllowed,
          PriceWithDelivery: deliveryPriceIncluded ? 1 : 0,
        };

        console.log(
          `📤 Отправка материала ${i + 1} с PurchReqId: ${purchReqId}`,
          tenderLine
        );

        const response = await fetch(
          `${API_CONFIG.BASE_URL}/api/PurchReqLines`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              ...authHeaders,
            },
            body: JSON.stringify(tenderLine),
          }
        );

        if (response.ok) {
          const result = await response.json();
          console.log(`✅ Материал ${i + 1} успешно добавлен:`, result);
          results.push(result);
        } else {
          const errorText = await response.text();
          console.error(
            `❌ Ошибка добавления материала ${i + 1}:`,
            response.status,
            errorText
          );
          console.error("❌ Данные материала:", tenderLine);
          throw new Error(
            `Ошибка создания строки для материала "${product.productName}": ${response.status} - ${errorText}`
          );
        }
      }

      console.log(
        `✅ Все ${results.length} материалов успешно добавлены к тендеру с PurchReqId: ${purchReqId}`
      );
      return results;
    } catch (error) {
      console.error("❌ Ошибка при создании строк тендера:", error);
      throw error;
    }
  };

  const handleRemoveProduct = (index) => {
    const removedProductId = formData[index].productId;

    // Удаляем из formData
    setFormData((prev) => prev.filter((_, i) => i !== index));

    // Обновляем selectedProducts
    const updatedProducts = selectedProducts.filter(
      (product) => product.MaterialId !== removedProductId
    );
    setSelectedProducts(updatedProducts);

    // Обновляем localStorage
    try {
      localStorage.setItem(
        "selectedTenderProducts",
        JSON.stringify(updatedProducts)
      );

      // Если это был последний товар, перенаправляем на CreateTender
      if (updatedProducts.length === 0) {
        router.push("/create-tender");
        return;
      }
    } catch (error) {
      console.error("Ошибка при обновлении localStorage:", error);
    }
  };

  const handleSubmit = async () => {
    // Валидация обязательных полей
    if (!tenderName.trim()) {
      alert("Укажите название закупки");
      return;
    }

    if (!deliveryDate) {
      alert("Укажите дату поставки");
      return;
    }

    if (!deliveryAddress.trim()) {
      alert("Укажите адрес доставки");
      return;
    }

    // Проверяем, что все товары имеют количество
    const incompleteProducts = formData.filter(
      (product) => !product.quantity || product.quantity <= 0
    );
    if (incompleteProducts.length > 0) {
      alert("Укажите количество для всех товаров");
      return;
    }

    // Проверяем авторизацию и данные компании
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    try {
      console.log("🚀 Начинаем создание тендера...");
      console.log("👤 Пользователь:", user);
      console.log("📝 Данные формы:", {
        tenderName,
        deliveryDate,
        deliveryAddress,
        selectedCityId,
        deliveryPriceIncluded,
        formData: formData.length,
        selectedProducts: selectedProducts.length,
      });

      // Шаг 1: Получаем новый PurchReqId
      const originalPurchReqId = await getPurchReqId();

      // Шаг 2: Генерируем DocNum и увеличиваем PurchReqId на 1
      const docNum = originalPurchReqId.toString().padStart(10, "0");
      const realPurchReqId = originalPurchReqId + 1;

      console.log(`📋 PurchReqId: ${originalPurchReqId} → ${realPurchReqId}`);
      console.log(`📄 DocNum: ${docNum}`);

      // Шаг 3: Создаем заголовок тендера с realPurchReqId
      console.log("📤 Создаем заголовок тендера...");
      const headerResult = await createTenderHeader(realPurchReqId, docNum);

      console.log(
        "📤 Создаем строки тендера для каждого материала отдельно..."
      );
      console.log(
        `📋 Используем тот же PurchReqId: ${realPurchReqId} для всех строк`
      );
      const linesResult = await createTenderLines(realPurchReqId);

      console.log("✅ Тендер успешно создан!");
      console.log("📊 Результат заголовка:", headerResult);
      console.log("📋 Результат строк:", linesResult);

      // Сохраняем номер тендера для отображения в уведомлении
      setCreatedTenderDocNum(docNum);

      // Очищаем localStorage после успешного создания тендера
      try {
        localStorage.removeItem("selectedTenderProducts");
        localStorage.removeItem("tenderAveragePrices");
        console.log("🧹 Данные очищены из localStorage");
      } catch (error) {
        console.error("⚠️ Ошибка при очистке localStorage:", error);
      }

      // Показываем уведомление об успешном создании
      setShowSuccessNotification(true);

      // Автоматически скрываем уведомление и переходим на создание тендера через 3 секунды
      setTimeout(() => {
        setShowSuccessNotification(false);
        router.push("/create-tender");
      }, 3000);
    } catch (error) {
      console.error("❌ Ошибка при создании тендера:", error);
      alert(`Ошибка при создании тендера: ${error.message}`);
    }
  };

  const handleClearAll = () => {
    // Очищаем localStorage
    try {
      localStorage.removeItem("selectedTenderProducts");
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    // Перенаправляем на CreateTender
    router.push("/create-tender");
  };

  const handleBack = () => {
    router.back();
  };

  const handleDeliveryPriceChoice = (includeDelivery) => {
    setDeliveryPriceIncluded(includeDelivery);
  };

  const handleAnalogChoice = (index, analogAllowed) => {
    handleFormDataChange(index, "analogAllowed", analogAllowed);
  };

  const handleFileUpload = (index, files) => {
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      const currentFiles = formData[index].attachedFiles || [];
      const newFiles = [...currentFiles, ...fileArray];
      handleFormDataChange(index, "attachedFiles", newFiles);
    }
  };

  const handleRemoveFile = (productIndex, fileIndex) => {
    const currentFiles = formData[productIndex].attachedFiles || [];
    const newFiles = currentFiles.filter((_, index) => index !== fileIndex);
    handleFormDataChange(productIndex, "attachedFiles", newFiles);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (selectedProducts.length === 0) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <Title>Нет выбранных товаров для создания тендера</Title>
          <NoProductsMessage>
            Сначала выберите товары на странице создания тендера
          </NoProductsMessage>
          <BackButton onClick={() => router.push("/create-tender")}>
            ПЕРЕЙТИ К ВЫБОРУ ТОВАРОВ
          </BackButton>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  return (
    <>
      {/* Уведомление об успешном создании тендера */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            Тендер успешно опубликован! Ваш номер тендера: {createdTenderDocNum}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img src="/icons/arrow_back_24px.svg" alt="Назад" />
            ВЕРНУТЬСЯ К ПОИСКУ
          </BackButton>

          <ActionButtons>
            <ClearAllButton onClick={handleClearAll}>
              <img
                src="/icons/BusketCreateTender.svg"
                width={"13"}
                height={"13"}
                alt="Очистить"
              />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton>
            <CreateTenderButton onClick={handleSubmit}>
              СОЗДАТЬ ТЕНДЕР
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
                alt="Создать"
              />
            </CreateTenderButton>
          </ActionButtons>
        </HeaderContent>
      </Header>

      <TenderFormContainer>
        <ContentContainer>
          <Title>Создание тендера на закуп</Title>

          <SectionTitle>Детали закупки</SectionTitle>
          <Text>
            Укажите необходимо количество позиций, цену которую готовы <br />
            предложить и пожелания к заказу.
          </Text>

          {formData.map((product, index) => (
            <ProductFormCard key={product.productId}>
              <ProductHeader>
                <ProductInfo>
                  <ProductId>{product.productId}</ProductId>
                  <Label>Единица измерения: {product.productUnit}</Label>
                  <ProductTitle>{product.productName}</ProductTitle>

                  <Label style={{ marginBottom: 0 }}>
                    Продается в штуках, используется для строительных работ.
                  </Label>
                </ProductInfo>
                <ClearAllButtonContainer>
                  <ClearAllButton
                    onClick={() => handleRemoveProduct(index)}
                    style={{ padding: "12px" }}
                  >
                    <img
                      src="/icons/BusketCreateTender.svg"
                      width={"16"}
                      height={"16"}
                      alt="Удалить"
                    />
                  </ClearAllButton>
                </ClearAllButtonContainer>
              </ProductHeader>

              <FormRow>
                <SmallFormGroup>
                  <Input
                    type="number"
                    value={product.quantity}
                    onChange={(e) =>
                      handleFormDataChange(index, "quantity", e.target.value)
                    }
                    placeholder="Кол-во"
                    required
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    шт.
                  </span>
                </SmallFormGroup>

                <SmallFormGroup>
                  <Input
                    type="number"
                    value={product.maxPrice}
                    onChange={(e) =>
                      handleFormDataChange(index, "maxPrice", e.target.value)
                    }
                    placeholder={
                      getRecommendedPrice(product.productId)
                        ? `Рекомендуемая: ${getRecommendedPrice(
                            product.productId
                          )} ₸`
                        : "Ваша цена"
                    }
                    style={{ paddingRight: "77px" }}
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    ₸ за шт.
                  </span>
                </SmallFormGroup>

                <ActionButtonContainer>
                  <ActionButton
                    active={product.analogAllowed === true}
                    onClick={() => handleAnalogChoice(index, true)}
                  >
                    Можно аналог
                  </ActionButton>
                  <ActionButton
                    active={product.analogAllowed === false}
                    onClick={() => handleAnalogChoice(index, false)}
                  >
                    Только это
                  </ActionButton>
                </ActionButtonContainer>
              </FormRow>

              <Label style={{ fontSize: "17px", color: "#656D78" }}>
                Пожелания к заказу
              </Label>

              <TextArea
                value={product.description}
                onChange={(e) =>
                  handleFormDataChange(index, "description", e.target.value)
                }
                placeholder="Дополнительные требования к товару"
              />

              <UploadButton
                onClick={() =>
                  document.getElementById(`file-input-${index}`).click()
                }
              >
                <img src="/icons/Upload.svg" alt="Загрузить" />
                <UploadText>Прикрепить файл</UploadText>
              </UploadButton>

              <HiddenFileInput
                id={`file-input-${index}`}
                type="file"
                multiple
                onChange={(e) => handleFileUpload(index, e.target.files)}
              />

              {product.attachedFiles && product.attachedFiles.length > 0 && (
                <AttachedFilesList>
                  {product.attachedFiles.map((file, fileIndex) => (
                    <AttachedFileItem key={fileIndex}>
                      <FileInfo>
                        <FileName>{file.name}</FileName>
                        <FileSize>{formatFileSize(file.size)}</FileSize>
                      </FileInfo>
                      <RemoveFileButton
                        onClick={() => handleRemoveFile(index, fileIndex)}
                        title="Удалить файл"
                      >
                        ×
                      </RemoveFileButton>
                    </AttachedFileItem>
                  ))}
                </AttachedFilesList>
              )}
            </ProductFormCard>
          ))}

          <DeliverySection>
            <SectionTitle>Учесть цену доставки в предложениях?</SectionTitle>
            <DeliveryText>
              Мы предупредим поставщиков о необходимости подачи цены
              <br />с учетом доставки до вашего адреса.
            </DeliveryText>

            <ActionButtonContainer>
              <ActionButton
                active={deliveryPriceIncluded === true}
                onClick={() => handleDeliveryPriceChoice(true)}
              >
                Цена с доставкой
              </ActionButton>
              <ActionButton
                active={deliveryPriceIncluded === false}
                onClick={() => handleDeliveryPriceChoice(false)}
              >
                Цена без доставки
              </ActionButton>
            </ActionButtonContainer>
          </DeliverySection>

          <DatePickerContainer>
            <SectionTitle>Срок поставки</SectionTitle>
            <Text>Укажите дату когда необходимо осуществить поставку</Text>

            <CalendarContainer>
              <DatePicker
                selected={deliveryDate}
                onChange={(date) => setDeliveryDate(date)}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                inline
                minDate={new Date()} // Не позволяем выбирать прошедшие даты
              />
            </CalendarContainer>
          </DatePickerContainer>

          <AddressContainer>
            <SectionTitle>Адрес доставки</SectionTitle>
            <Text>
              Укажите адрес куда необходимо доставить товар, или где его забрать
            </Text>

            <AddressRow>
              <TenderInputContainer>
                <Label>Название улицы, дом, строение</Label>
                <AddressInput
                  type="text"
                  value={deliveryAddress}
                  onChange={(e) => setDeliveryAddress(e.target.value)}
                  placeholder="Укажите адрес доставки"
                  required
                />
              </TenderInputContainer>

              <div>
                <Label>Город</Label>
                <CityDropdown data-city-dropdown>
                  <CityButton onClick={toggleCityDropdown} type="button">
                    {getCurrentCity().RegionName}
                    <span
                      style={{
                        transform: isCityDropdownOpen
                          ? "rotate(180deg)"
                          : "rotate(0deg)",
                        transition: "transform 0.2s ease",
                      }}
                    >
                      ▼
                    </span>
                  </CityButton>
                  {isCityDropdownOpen && (
                    <CityDropdownList>
                      {cities.map((cityOption) => (
                        <CityOption
                          key={cityOption.RegionId}
                          onClick={() => handleCitySelect(cityOption)}
                        >
                          <span>{cityOption.RegionName}</span>
                          <CityCheckbox
                            checked={selectedCityId === cityOption.RegionId}
                          />
                        </CityOption>
                      ))}
                    </CityDropdownList>
                  )}
                </CityDropdown>
              </div>
            </AddressRow>
          </AddressContainer>

          <SectionTitle>Название закупки</SectionTitle>
          <Text>
            Придумайте название закупки для удобства в списке ваших закупок
          </Text>

          <TenderNameContainer>
            <TenderInputContainer>
              <Label>Название закупки</Label>
              <TenderNameInput
                type="text"
                value={tenderName}
                onChange={(e) => setTenderName(e.target.value)}
                placeholder="Название закупки"
                required
              />
            </TenderInputContainer>

            {/* <TenderInputContainer>
              <Label>№ Закупки</Label>
              <TenderNumberInput
                type="text"
                value={tenderNumber}
                onChange={(e) => setTenderNumber(e.target.value)}
                placeholder="№ Закупки"
              />
            </TenderInputContainer> */}
          </TenderNameContainer>

          <div style={{ textAlign: "center" }}>
            <PublishButton onClick={handleSubmit}>
              ОПУБЛИКОВАТЬ ТЕНДЕР
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
                alt="Опубликовать"
              />
            </PublishButton>
          </div>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default TenderFormClient;
