import ISR_CONFIG, { ISRUtils } from "../../config/isr";

// Базовый URL сайта
const BASE_URL = "https://shop.sadi.kz";

// Генерация sitemap для страниц товаров (ограниченное количество)
export async function GET() {
  try {
    // ЭКСТРЕННАЯ МЕРА: Резко уменьшаем количество страниц
    const maxPages = 50; // Только 50 страниц каталога

    ISRUtils.logISROperation("sitemap-pages", {
      maxPages,
      note: "Ограничено для экономии invocations",
    });

    // Создаем XML для ограниченного количества страниц
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Добавляем страницы с оптимизированными приоритетами
    for (let page = 1; page <= maxPages; page++) {
      const priority = ISR_CONFIG.getProductPriority(page, page);
      const changefreq = ISR_CONFIG.getChangeFrequency("catalog", page);

      xml += `
  <url>
    <loc>${BASE_URL}/products/page/${page}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
    }

    xml += `
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600", // Кэшируем на 1 час
      },
    });
  } catch (error) {
    console.error("Ошибка при генерации sitemap-pages:", error);

    // Возвращаем пустой sitemap в случае ошибки
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new Response(xml, {
      headers: {
        "Content-Type": "application/xml",
      },
    });
  }
}
