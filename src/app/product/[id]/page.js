import { notFound } from "next/navigation";
import Layout from "../../../components/Layout";
import ProductDetailClient from "./ProductDetailClient";
import ApiService from "../../../services/api.service";
import ISR_CONFIG, { ISRUtils } from "../../../config/isr";
import {
  ProductSchema,
  BreadcrumbSchema,
} from "../../../components/StructuredData";

// ISR: Генерация статических путей для популярных товаров
export async function generateStaticParams() {
  try {
    // Генерируем только самые популярные товары при билде
    const limit = ISR_CONFIG.STATIC_GENERATION_LIMITS.PRODUCTS_PREBUILD;
    const response = await ApiService.getProductsWithPagination(1, limit);
    const products = response.data || response.products || [];

    ISRUtils.logISROperation("generateStaticParams", {
      type: "products",
      generated: products.length,
      limit,
    });

    return products.map((product) => ({
      id: product.MaterialId.toString(),
    }));
  } catch (error) {
    console.error("ISR: Ошибка при генерации статических путей:", error);
    // Возвращаем пустой массив, все страницы будут генерироваться по требованию
    return [];
  }
}

// ISR: Настройка времени ревалидации (30 дней)
export const revalidate = 2592000; // 30 дней в секундах

// Генерация метаданных для SEO
export async function generateMetadata({ params }) {
  const productId = params.id;

  try {
    // Получаем данные о товаре на сервере
    const product = await ApiService.getProductById(productId);

    if (!product) {
      return {
        title: "Товар не найден",
        description: "Запрашиваемый товар не найден в каталоге",
      };
    }

    // Генерируем SEO-оптимизированные мета-теги
    const materialName = product.MaterialName.toLowerCase();
    const title = `Купить ${product.MaterialName} в Казахстане - цена от ${
      product.RetailPrice || "уточняйте"
    } тенге`;
    const description = `${
      product.MaterialName
    } ⭐ Лучшие цены в Казахстане ⭐ От ${
      product.RetailPrice || "уточняйте"
    } тенге ⭐ ${
      product.SuppliersCount || 0
    } поставщиков ⭐ Быстрая доставка ⭐ Гарантия качества`;

    // Генерируем расширенные ключевые слова на основе названия материала
    const generateKeywords = (materialName) => {
      const baseKeywords = [
        product.MaterialName,
        `купить ${materialName}`,
        `${materialName} цена`,
        `${materialName} казахстан`,
        `${materialName} доставка`,
        `заказать ${materialName}`,
        `${materialName} оптом`,
        `${materialName} розница`,
      ];

      // Добавляем специфичные ключевые слова в зависимости от типа материала
      if (materialName.includes("бетон")) {
        baseKeywords.push(
          "бетон марки",
          "бетонная смесь",
          "товарный бетон",
          "бетон класса"
        );
      }
      if (materialName.includes("щебень")) {
        baseKeywords.push(
          "щебень фракции",
          "гранитный щебень",
          "известняковый щебень",
          "щебень для дорог"
        );
      }
      if (materialName.includes("песок")) {
        baseKeywords.push(
          "строительный песок",
          "речной песок",
          "карьерный песок",
          "песок для бетона"
        );
      }
      if (materialName.includes("кирпич")) {
        baseKeywords.push(
          "строительный кирпич",
          "облицовочный кирпич",
          "керамический кирпич",
          "силикатный кирпич"
        );
      }
      if (materialName.includes("цемент")) {
        baseKeywords.push(
          "портландцемент",
          "цемент марки",
          "цемент м400",
          "цемент м500"
        );
      }

      return baseKeywords;
    };

    return {
      title,
      description,
      keywords: generateKeywords(materialName),
      openGraph: {
        title,
        description,
        type: "website",
        url: `https://shop.sadi.kz/product/${productId}`,
        siteName: "SADI Shop",
        images: [
          {
            url: "/images/placeholder.png",
            width: 800,
            height: 600,
            alt: product.MaterialName,
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: ["/images/placeholder.png"],
      },
      alternates: {
        canonical: `https://shop.sadi.kz/product/${productId}`,
      },
      robots: {
        index: true,
        follow: true,
      },
      other: {
        // Кастомные OpenGraph теги для товара
        "og:type": "product",
        "product:price:amount": product.RetailPrice || 0,
        "product:price:currency": "KZT",
        "product:availability": "in stock",
        "product:condition": "new",
        "product:retailer_item_id": product.MaterialId,
      },
    };
  } catch (error) {
    console.error("Ошибка при генерации метаданных товара:", error);
    return {
      title: "Товар не найден",
      description: "Запрашиваемый товар не найден в каталоге",
    };
  }
}

// Серверный компонент для получения данных товара
async function getProductData(productId) {
  try {
    console.log("SSR Product: Загружаем данные товара", productId);

    const product = await ApiService.getProductById(productId);

    if (!product) {
      return null;
    }

    console.log("SSR Product: Товар загружен:", product.MaterialName);
    return product;
  } catch (error) {
    console.error("SSR Product: Ошибка при получении данных товара:", error);
    return null;
  }
}

// Основной серверный компонент страницы
export default async function ProductPage({ params }) {
  const productId = params.id;

  // Валидация ID товара
  if (!productId) {
    notFound();
  }

  // Получаем данные товара на сервере
  const product = await getProductData(productId);

  // Если товар не найден, показываем 404
  if (!product) {
    notFound();
  }

  // Формируем расширенные структурированные данные для поисковых систем
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.MaterialName,
    description:
      product.Description ||
      `${product.MaterialName} - строительный материал высокого качества. Купить с доставкой по Казахстану.`,
    sku: product.MaterialId.toString(),
    mpn: product.MaterialId.toString(), // Manufacturer Part Number
    gtin: product.MaterialId.toString(), // Global Trade Item Number
    brand: {
      "@type": "Brand",
      name: "SADI Shop",
    },
    category: "Строительные материалы",
    offers: {
      "@type": "Offer",
      price: product.RetailPrice || 0,
      priceCurrency: "KZT",
      availability: "https://schema.org/InStock",
      priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0], // 30 дней
      itemCondition: "https://schema.org/NewCondition",
      seller: {
        "@type": "Organization",
        name: "Строительный маркетплейс",
        url: "https://shop.sadi.kz",
      },
      shippingDetails: {
        "@type": "OfferShippingDetails",
        shippingRate: {
          "@type": "MonetaryAmount",
          value: "0",
          currency: "KZT",
        },
        deliveryTime: {
          "@type": "ShippingDeliveryTime",
          handlingTime: {
            "@type": "QuantitativeValue",
            minValue: 1,
            maxValue: 3,
            unitCode: "DAY",
          },
        },
      },
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.5",
      reviewCount: "10",
      bestRating: "5",
      worstRating: "1",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: {
          "@type": "Person",
          name: "Строительная компания",
        },
        reviewBody: `Качественный ${product.MaterialName}. Быстрая доставка, соответствует заявленным характеристикам.`,
      },
    ],
    image: "/images/placeholder.png",
    url: `https://shop.sadi.kz/product/${productId}`,
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Материал",
        value: product.MaterialName,
      },
      {
        "@type": "PropertyValue",
        name: "Поставщики",
        value: `${product.SuppliersCount || 0} поставщиков`,
      },
    ],
  };

  return (
    <Layout>
      {/* Структурированные данные для поисковых систем */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Хлебные крошки для SEO */}
      <nav
        aria-label="Хлебные крошки"
        style={{
          padding: "16px 24px",
          fontSize: "14px",
        }}
      >
        <a href="/" style={{ color: "#666", textDecoration: "none" }}>
          Главная
        </a>
        <span style={{ margin: "0 8px", color: "#999" }}>→</span>
        <a
          href="/products/page/1"
          style={{ color: "#666", textDecoration: "none" }}
        >
          Товары
        </a>
        <span style={{ margin: "0 8px", color: "#999" }}>→</span>
        <span style={{ color: "#333" }}>{product.MaterialName}</span>
      </nav>

      {/* Клиентский компонент для интерактивности */}
      <ProductDetailClient product={product} />
    </Layout>
  );
}
