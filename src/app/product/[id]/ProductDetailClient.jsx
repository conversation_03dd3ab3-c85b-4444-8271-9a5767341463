"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { useCart } from "../../../context/CartContext";
import { useProductImages } from "../../../hooks/useProductImage";
import { useAveragePricesContext } from "../../../context/AveragePricesContext";
import { useSuppliers } from "../../../hooks/useApi";

const ProductDetailContainer = styled.div`
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
ProductDetailContainer.displayName = "ProductDetailContainer";

const BackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 16px;

  &:hover {
    color: #0066cc;
  }

  svg {
    margin-right: 4px;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: bold;
  max-width: 656px;
  color: #333;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    font-size: 24px;
    margin-bottom: 16px;
  }
`;
Title.displayName = "Title";

const ProductContent = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;
ProductContent.displayName = "ProductContent";

const ProductImages = styled.div`
  flex: 0 0 65%;
  border-radius: 8px;
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    flex: none;
    flex-direction: column;
    gap: 12px;
  }
`;
ProductImages.displayName = "ProductImages";

const ThumbnailsColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: row;
    gap: 8px;
    order: 2;
    overflow-x: auto;
    padding-bottom: 8px;
  }
`;
ThumbnailsColumn.displayName = "ThumbnailsColumn";

const Thumbnail = styled.div`
  width: 176px;
  height: 176px;
  border: 1px solid ${(props) => (props.$active ? "#0066cc" : "#e0e0e0")};
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;

  @media (max-width: 768px) {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: white;
  }
`;
Thumbnail.displayName = "Thumbnail";

const MainImage = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;

  @media (max-width: 768px) {
    order: 1;
    height: 250px;
    border: 1px solid #e0e0e0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: white;
  }
`;
MainImage.displayName = "MainImage";

const ProductInfo = styled.div`
  max-width: 272px;
  max-height: fit-content;
  background-color: white;
  border-radius: 8px;
  padding: 24px 16.5px;

  @media (max-width: 768px) {
    max-width: none;
    max-height: none;
    padding: 16px;
  }
`;
ProductInfo.displayName = "ProductInfo";

const ProductInfoTitle = styled.h2`
  font-size: 17px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  line-height: 150%;
`;
ProductInfoTitle.displayName = "ProductInfoTitle";

const PriceInfo = styled.div`
  margin-bottom: 24px;
`;
PriceInfo.displayName = "PriceInfo";

const PriceRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 14px;
  color: #808185;
  border-bottom: 1px solid #ddd;
  padding-bottom: 8px;
`;
PriceRow.displayName = "PriceRow";

const Price = styled.span`
  font-weight: ${(props) => (props.$bold ? "600" : "400")};
`;
Price.displayName = "Price";

const AddToCartButton = styled.button`
  width: 100%;
  padding: 12px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
AddToCartButton.displayName = "AddToCartButton";

const TabsContainer = styled.div`
  margin-top: 24px;
`;
TabsContainer.displayName = "TabsContainer";

const TabsHeader = styled.div`
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 24px;
`;
TabsHeader.displayName = "TabsHeader";

const Tab = styled.button`
  padding: 16px 24px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: ${(props) => (props.$active ? "#0066cc" : "#666")};
  border-bottom: 2px solid
    ${(props) => (props.$active ? "#0066cc" : "transparent")};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #0066cc;
  }
`;
Tab.displayName = "Tab";

const TabContent = styled.div`
  display: ${(props) => (props.$active ? "block" : "none")};
`;
TabContent.displayName = "TabContent";

const SuppliersTable = styled.table`
  width: 100%;
  border-collapse: collapse;

  th {
    background-color: #f8f9fa;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    border-bottom: 1px solid #e0e0e0;
  }

  td {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
  }

  tr:hover {
    background-color: #f8f9fa;
  }
`;
SuppliersTable.displayName = "SuppliersTable";

const SupplierButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
SupplierButton.displayName = "SupplierButton";

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;

  th {
    background-color: #f8f9fa;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid #e0e0e0;
    width: 30%;
  }

  td {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
  }
`;
Table.displayName = "Table";

const SEOContent = styled.div`
  margin-top: 48px;
  padding: 32px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;

  @media (max-width: 768px) {
    margin-top: 32px;
    padding: 24px 16px;
  }
`;
SEOContent.displayName = "SEOContent";

const SEOTitle = styled.h2`
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 24px;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 24px;
    margin-bottom: 20px;
  }
`;
SEOTitle.displayName = "SEOTitle";

const SEOText = styled.div`
  font-size: 16px;
  line-height: 1.6;
  color: #555;

  p {
    margin-bottom: 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 24px 0 16px 0;
  }

  ul,
  ol {
    margin: 16px 0;
    padding-left: 24px;
  }

  li {
    margin-bottom: 8px;
  }

  strong {
    color: #0066cc;
    font-weight: 600;
  }

  @media (max-width: 768px) {
    font-size: 14px;

    h3 {
      font-size: 18px;
      margin: 20px 0 12px 0;
    }

    ul,
    ol {
      padding-left: 20px;
    }
  }
`;
SEOText.displayName = "SEOText";

export default function ProductDetailClient({ product }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("sellers");
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const { addToCart } = useCart();
  const { updatePricesForProducts, getPriceByMaterialId } =
    useAveragePricesContext();

  // Получаем поставщиков для данного товара
  const {
    data: suppliers = [],
    isLoading: suppliersLoading,
    error: suppliersError,
  } = useSuppliers(product?.MaterialId);

  // Диагностика: проверяем, что получили продукт
  console.log("ProductDetailClient: Получен продукт:", product);
  console.log("ProductDetailClient: Поставщики:", suppliers);

  // Проверяем, что продукт существует
  if (!product) {
    console.error("ProductDetailClient: Продукт не передан!");
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <h1>Ошибка загрузки товара</h1>
        <p>Данные товара не найдены</p>
        <button onClick={() => router.back()}>Назад</button>
      </div>
    );
  }

  // Используем хук для загрузки изображений товара
  const { images: productImages } = useProductImages(product.MaterialId);

  // Обновляем средние цены и фотографии для текущего продукта
  useEffect(() => {
    if (product) {
      // Теперь updatePricesForProducts обновляет и цены, и фотографии
      updatePricesForProducts([product]);
    }
  }, [product, updatePricesForProducts]);

  // Функция для получения актуальных цен продукта
  const getProductPrices = () => {
    if (!product)
      return { retailPrice: null, wholesalePrice: null, companyCnt: 0 };

    const averagePrice = getPriceByMaterialId(product.MaterialId);

    return {
      retailPrice: averagePrice?.AvgRetailPrice || product.RetailPrice,
      wholesalePrice: averagePrice?.AvgTradePrice || product.WholesalePrice,
      companyCnt: averagePrice?.CompanyCnt || product.SuppliersCount || 0,
    };
  };

  const handleBack = () => {
    router.back();
  };

  // Функция для добавления товара в корзину от конкретного поставщика
  const handleAddToCart = (supplier) => {
    const productImage =
      productImages.length > 0
        ? productImages[0].full
        : "/images/placeholder.png";

    const cartItem = {
      id: product.MaterialId + "-" + supplier.AdvertId,
      title: `${product.MaterialName} (${supplier.CompanyName})`,
      retailPrice: supplier.RetailPrice
        ? parseFloat(supplier.RetailPrice)
        : null,
      wholesalePrice: supplier.TradePrice
        ? parseFloat(supplier.TradePrice)
        : null,
      image: productImage,
      supplierId: supplier.AdvertId,
      supplierName: supplier.CompanyName,
      // Флаг, что это товар от конкретного поставщика
      isFromSpecificSupplier: true,
      // Сохраняем оригинальные цены поставщика
      originalRetailPrice: supplier.RetailPrice
        ? parseFloat(supplier.RetailPrice)
        : null,
      originalWholesalePrice: supplier.TradePrice
        ? parseFloat(supplier.TradePrice)
        : null,
    };

    addToCart(cartItem);
    alert("Товар добавлен в корзину");
  };

  // Функция для добавления товара в корзину с общими ценами
  const handleAddToCartGeneral = () => {
    const prices = getProductPrices();
    const productImage =
      productImages.length > 0
        ? productImages[0].full
        : "/images/placeholder.png";

    const cartItem = {
      id: product.MaterialId,
      title: product.MaterialName,
      retailPrice: prices.retailPrice,
      wholesalePrice: prices.wholesalePrice,
      image: productImage,
      // Для общих товаров флаг не устанавливаем
      isFromSpecificSupplier: false,
    };

    addToCart(cartItem);
    alert("Товар добавлен в корзину");
  };

  return (
    <ProductDetailContainer as="article">
      <BackButton onClick={handleBack} aria-label="Вернуться назад">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path
            d="M10 12L6 8L10 4"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        Назад
      </BackButton>

      <Title as="h1">{product.MaterialName}</Title>

      <ProductContent>
        <ProductImages as="section" aria-label="Изображения продукта">
          <ThumbnailsColumn>
            {productImages.map((image, index) => (
              <Thumbnail
                key={index}
                $active={activeImageIndex === index}
                onClick={() => setActiveImageIndex(index)}
                aria-label={`Миниатюра ${index + 1}`}
                aria-pressed={activeImageIndex === index}
              >
                <img src={image.thumbnail} alt={`${image.alt} - миниатюра`} />
              </Thumbnail>
            ))}
          </ThumbnailsColumn>
          <MainImage>
            <img
              src={
                productImages[activeImageIndex]?.full ||
                "/images/placeholder.png"
              }
              alt={productImages[activeImageIndex]?.alt || "Изображение товара"}
            />
          </MainImage>
        </ProductImages>

        <ProductInfo as="section" aria-label="Информация о продукте">
          <ProductInfoTitle as="h2">{product.MaterialName}</ProductInfoTitle>

          <PriceInfo>
            <PriceRow>
              <span>Единица измерения:</span>
              <Price bold> {product.UnitId}</Price>
            </PriceRow>
            <PriceRow>
              <span>Сред. розн.:</span>
              <Price $bold>
                {(() => {
                  const prices = getProductPrices();
                  return prices.retailPrice
                    ? `${prices.retailPrice} ₸`
                    : "По запросу";
                })()}
              </Price>
            </PriceRow>
            <PriceRow>
              <span>Сред. опт.:</span>
              <Price>
                {(() => {
                  const prices = getProductPrices();
                  return prices.wholesalePrice
                    ? `${prices.wholesalePrice} ₸`
                    : "По запросу";
                })()}
              </Price>
            </PriceRow>
            <PriceRow>
              <span>Кол-во поставщиков</span>
              <Price $bold>{getProductPrices().companyCnt}</Price>
            </PriceRow>
          </PriceInfo>

          <AddToCartButton
            onClick={handleAddToCartGeneral}
            aria-label={`Добавить ${product.MaterialName} в корзину`}
          >
            В корзину
          </AddToCartButton>
        </ProductInfo>
      </ProductContent>

      <TabsContainer as="section" aria-label="Детальная информация">
        <TabsHeader role="tablist">
          <Tab
            role="tab"
            id="tab-sellers"
            aria-controls="panel-sellers"
            aria-selected={activeTab === "sellers"}
            $active={activeTab === "sellers"}
            onClick={() => setActiveTab("sellers")}
          >
            ПРОДАВЦЫ
          </Tab>
          {/* <Tab
            role="tab"
            id="tab-specs"
            aria-controls="panel-specs"
            aria-selected={activeTab === "specs"}
            $active={activeTab === "specs"}
            onClick={() => setActiveTab("specs")}
          >
            ХАРАКТЕРИСТИКИ
          </Tab>
          <Tab
            role="tab"
            id="tab-description"
            aria-controls="panel-description"
            aria-selected={activeTab === "description"}
            $active={activeTab === "description"}
            onClick={() => setActiveTab("description")}
          >
            ОПИСАНИЕ
          </Tab> */}
        </TabsHeader>

        <TabContent
          role="tabpanel"
          id="panel-sellers"
          aria-labelledby="tab-sellers"
          $active={activeTab === "sellers"}
        >
          {suppliersLoading ? (
            <p>Загрузка поставщиков...</p>
          ) : suppliersError ? (
            <p>Ошибка при загрузке поставщиков</p>
          ) : suppliers.length > 0 ? (
            <SuppliersTable>
              <thead>
                <tr>
                  <th>ПОСТАВЩИК</th>

                  <th>РОЗНИЦА</th>
                  <th>ОПТ</th>
                  <th>ОПТ ОТ</th>
                  <th>ЕД. ИЗМ.</th>
                  <th>РЕГИОН</th>
                  <th>ДЕЙСТВИЕ</th>
                </tr>
              </thead>
              <tbody>
                {suppliers.map((supplier, index) => (
                  <tr key={supplier.AdvertId || index}>
                    <td>{supplier.CompanyName}</td>
                    <td>
                      {supplier.RetailPrice
                        ? `${supplier.RetailPrice.toLocaleString()} ₸`
                        : "По запросу"}
                    </td>
                    <td>
                      {supplier.TradePrice
                        ? `${supplier.TradePrice.toLocaleString()} ₸`
                        : "По запросу"}
                    </td>
                    <td>
                      {supplier.FromUnit > 0
                        ? supplier.FromUnit.toLocaleString()
                        : "-"}
                    </td>
                    <td>{supplier.UnitId || "-"}</td>
                    <td>{supplier.RegionId || "-"}</td>
                    <td>
                      <SupplierButton onClick={() => handleAddToCart(supplier)}>
                        В корзину
                      </SupplierButton>
                    </td>
                  </tr>
                ))}
              </tbody>
            </SuppliersTable>
          ) : (
            <p>Информация о поставщиках отсутствует</p>
          )}
        </TabContent>

        <TabContent
          role="tabpanel"
          id="panel-specs"
          aria-labelledby="tab-specs"
          $active={activeTab === "specs"}
        >
          <Table>
            <tbody>
              {product && product.Specifications ? (
                Object.entries(product.Specifications).map(([key, value]) => (
                  <tr key={key}>
                    <th scope="row">
                      {key === "Diameter"
                        ? "Диаметр"
                        : key === "Type"
                        ? "Тип"
                        : key === "Material"
                        ? "Материал"
                        : key === "Purpose"
                        ? "Назначение"
                        : key === "Thickness"
                        ? "Толщина"
                        : key === "Color"
                        ? "Цвет"
                        : key}
                    </th>
                    <td>{value}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="2">Характеристики не указаны</td>
                </tr>
              )}
            </tbody>
          </Table>
        </TabContent>

        <TabContent
          role="tabpanel"
          id="panel-description"
          aria-labelledby="tab-description"
          $active={activeTab === "description"}
        >
          {product && product.Description ? (
            <div>
              {product.Description.split(". ").map((sentence, index) => (
                <p key={index}>
                  {sentence.trim() + (sentence.endsWith(".") ? "" : ".")}
                </p>
              ))}
            </div>
          ) : (
            <p>Описание отсутствует</p>
          )}
        </TabContent>
      </TabsContainer>

      {/* SEO-контент для лучшей индексации */}
      <SEOContent>
        <SEOTitle as="h2">Купить {product.MaterialName} в Казахстане</SEOTitle>
        <SEOText>
          <p>
            <strong>{product.MaterialName}</strong> - высококачественный
            строительный материал, который вы можете купить в нашем маркетплейсе
            по выгодной цене. Мы предлагаем {product.MaterialName.toLowerCase()}{" "}
            от проверенных поставщиков с гарантией качества и быстрой доставкой
            по всему Казахстану.
          </p>

          <h3>
            Преимущества покупки {product.MaterialName.toLowerCase()} у нас:
          </h3>
          <ul>
            <li>Лучшие цены от {product.RetailPrice || "уточняйте"} тенге</li>
            {getProductPrices().companyCnt !== 0 && (
              <li>
                <strong>{getProductPrices().companyCnt}</strong> проверенных
                поставщиков
              </li>
            )}

            <li>Быстрая доставка по Казахстану</li>
            <li>Гарантия качества материалов</li>
            <li>Возможность покупки оптом и в розницу</li>
            <li>Профессиональная консультация</li>
          </ul>

          <h3>Как заказать {product.MaterialName.toLowerCase()}:</h3>
          <ol>
            <li>Выберите подходящего поставщика из списка</li>
            <li>Добавьте товар в корзину</li>
            <li>Оформите заказ с указанием адреса доставки</li>
            <li>
              Получите {product.MaterialName.toLowerCase()} в удобное время
            </li>
          </ol>

          <p>
            Заказывая {product.MaterialName.toLowerCase()} в нашем маркетплейсе,
            вы получаете доступ к широкому ассортименту строительных материалов
            от ведущих производителей и поставщиков Казахстана. Наша платформа
            обеспечивает прозрачность цен и качество обслуживания.
          </p>
        </SEOText>
      </SEOContent>
    </ProductDetailContainer>
  );
}
