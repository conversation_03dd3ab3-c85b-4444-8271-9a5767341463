"use client";

import React from "react";
import styled from "styled-components";

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
  text-align: center;
  background-color: #f8f9fa;
`;
ErrorContainer.displayName = "ErrorContainer";

const ErrorIcon = styled.div`
  font-size: 80px;
  color: #dc3545;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    font-size: 60px;
    margin-bottom: 16px;
  }
`;
ErrorIcon.displayName = "ErrorIcon";

const ErrorTitle = styled.h1`
  font-size: 32px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 24px;
    margin-bottom: 12px;
  }
`;
ErrorTitle.displayName = "ErrorTitle";

const ErrorMessage = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  max-width: 500px;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 14px;
    margin-bottom: 24px;
  }
`;
ErrorMessage.displayName = "ErrorMessage";

const ButtonContainer = styled.div`
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    width: 100%;
    max-width: 300px;
  }
`;
ButtonContainer.displayName = "ButtonContainer";

const RetryButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
RetryButton.displayName = "RetryButton";

const HomeButton = styled.button`
  background-color: white;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #0066cc;
  }
`;
HomeButton.displayName = "HomeButton";

const ErrorDetails = styled.details`
  margin-top: 32px;
  max-width: 600px;
  text-align: left;

  summary {
    cursor: pointer;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
  }

  pre {
    background-color: #f1f3f4;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    color: #333;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;
ErrorDetails.displayName = "ErrorDetails";

export default function ErrorPage({ error, reset }) {
  const handleGoHome = () => {
    if (typeof window !== "undefined") {
      window.location.href = "/";
    }
  };

  return (
    <ErrorContainer>
      <ErrorIcon>⚠️</ErrorIcon>
      <ErrorTitle>Произошла ошибка</ErrorTitle>
      <ErrorMessage>
        К сожалению, что-то пошло не так. Попробуйте обновить страницу или
        вернуться на главную.
      </ErrorMessage>

      <ButtonContainer>
        <RetryButton onClick={reset}>Попробовать снова</RetryButton>
        <HomeButton onClick={handleGoHome}>На главную</HomeButton>
      </ButtonContainer>

      {process.env.NODE_ENV === "development" && error && (
        <ErrorDetails>
          <summary>Детали ошибки (только в режиме разработки)</summary>
          <pre>{error.message}</pre>
          {error.stack && <pre>{error.stack}</pre>}
        </ErrorDetails>
      )}
    </ErrorContainer>
  );
}
// dsa
