'use client';

import React, { useState } from "react";
import styled from "styled-components";
import Layout from "../../components/Layout";
import RegistrationWizard from "../../components/RegistrationWizard";

const TestContainer = styled.div`
  padding: 24px;
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 120px);
  background-color: #f8f9fa;

  @media (max-width: 768px) {
    padding: 16px;
    min-height: calc(100vh - 80px);
  }
`;

const TestPage = () => {
  const [showWizard, setShowWizard] = useState(true);
  const [completedData, setCompletedData] = useState(null);

  const handleComplete = (data) => {
    console.log("Регистрация завершена:", data);
    setCompletedData(data);
    setShowWizard(false);
  };

  const handleCancel = () => {
    console.log("Регистрация отменена");
    setShowWizard(false);
  };

  const resetTest = () => {
    setShowWizard(true);
    setCompletedData(null);
  };

  if (!showWizard && completedData) {
    return (
      <Layout>
        <TestContainer>
          <div style={{ textAlign: 'center', maxWidth: '600px' }}>
            <h2>Регистрация завершена!</h2>
            <pre style={{ 
              background: '#f8f9fa', 
              padding: '20px', 
              borderRadius: '8px',
              textAlign: 'left',
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {JSON.stringify(completedData, null, 2)}
            </pre>
            <button 
              onClick={resetTest}
              style={{
                padding: '12px 24px',
                backgroundColor: '#0066cc',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                marginTop: '20px'
              }}
            >
              Тестировать снова
            </button>
          </div>
        </TestContainer>
      </Layout>
    );
  }

  if (!showWizard) {
    return (
      <Layout>
        <TestContainer>
          <div style={{ textAlign: 'center' }}>
            <h2>Регистрация отменена</h2>
            <button 
              onClick={resetTest}
              style={{
                padding: '12px 24px',
                backgroundColor: '#0066cc',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                marginTop: '20px'
              }}
            >
              Тестировать снова
            </button>
          </div>
        </TestContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <TestContainer>
        <RegistrationWizard
          initialEmail="<EMAIL>"
          onComplete={handleComplete}
          onCancel={handleCancel}
        />
      </TestContainer>
    </Layout>
  );
};

export default TestPage;
