import { Suspense } from "react";
import Layout from "../../components/Layout";
import CreateTenderClient from "./CreateTenderClient";
import { ISRUtils } from "../../config/isr";

// Генерация мета-тегов для SEO
export async function generateMetadata() {
  return {
    title:
      "Создание тендера на закуп материалов | SADI Shop - интернет-магазин строительных материалов",
    description:
      "Создайте тендер на закуп строительных материалов в SADI Shop. Найдите материалы, добавьте в список закупок, укажите срок поставки и опубликуйте тендер для поставщиков в Казахстане.",
    keywords: [
      "SADI Shop",
      "САДИ Шоп",
      "sadi",
      "сади",
      "создать тендер",
      "тендер на закуп",
      "закуп материалов",
      "закупки",
      "строительные материалы",
      "стройка",
      "строительство",
      "тендер строительство",
      "закупка оборудования",
      "тендер инструменты",
      "создание тендера",
      "электронные торги",
      "государственные закупки",
      "коммерческие тендеры",
      "поставщики материалов",
      "Казахстан",
    ],
    openGraph: {
      title: "Создание тендера на закуп материалов",
      description:
        "Создайте тендер на закуп строительных материалов, инструментов и оборудования. Простой и удобный процесс создания тендера.",
      type: "website",
      url: "https://shop.sadi.kz/create-tender",
      siteName: "SADI.KZ",
      images: [
        {
          url: "/images/tender-og.jpg",
          width: 1200,
          height: 630,
          alt: "Создание тендера на закуп материалов",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Создание тендера на закуп материалов",
      description:
        "Создайте тендер на закуп строительных материалов, инструментов и оборудования.",
      images: ["/images/tender-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: "https://shop.sadi.kz/create-tender",
    },
  };
}

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Создание тендера на закуп материалов",
  description:
    "Создайте тендер на закуп строительных материалов, инструментов и оборудования",
  url: "https://shop.sadi.kz/create-tender",
  mainEntity: {
    "@type": "Service",
    name: "Создание тендера",
    description:
      "Сервис для создания тендеров на закуп строительных материалов",
    provider: {
      "@type": "Organization",
      name: "SADI.KZ",
      url: "https://shop.sadi.kz",
    },
    serviceType: "Электронные торги",
    areaServed: {
      "@type": "Country",
      name: "Казахстан",
    },
  },
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Создание тендера",
        item: "https://shop.sadi.kz/create-tender",
      },
    ],
  },
};

// Компонент загрузки
function CreateTenderLoading() {
  return (
    <div
      style={{
        padding: "40px 20px",
        textAlign: "center",
        minHeight: "400px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div>
        <h1>Создание тендера</h1>
        <p>Загрузка каталога материалов...</p>
        <div
          style={{
            width: "40px",
            height: "40px",
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #0066cc",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "20px auto",
          }}
        />
      </div>
    </div>
  );
}

// ISR: Кэширование страницы создания тендера на 7 дней
export const revalidate = 604800; // 7 дней в секундах

// Основной серверный компонент
export default function CreateTenderPage() {
  // Логируем ISR операцию
  ISRUtils.logISROperation("create-tender-page", {
    revalidate: 604800,
    note: "Страница создания тендера с ISR кэшированием",
  });
  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO-контент для поисковых систем */}
      <div style={{ display: "none" }}>
        <h1>Создание тендера на закуп строительных материалов</h1>
        <h2>Как создать тендер на закуп материалов</h2>
        <p>
          Создание тендера на нашей платформе - это простой и удобный способ
          найти лучших поставщиков строительных материалов, инструментов и
          оборудования. Следуйте простым шагам: найдите нужные материалы,
          добавьте их в список закупок, укажите требования и сроки поставки,
          опубликуйте тендер.
        </p>
        <h3>Преимущества создания тендера</h3>
        <ul>
          <li>Конкурентные цены от множества поставщиков</li>
          <li>Экономия времени на поиск поставщиков</li>
          <li>Прозрачный процесс выбора</li>
          <li>Гарантия качества материалов</li>
        </ul>
      </div>

      {/* Клиентский компонент с Suspense */}
      <Suspense fallback={<CreateTenderLoading />}>
        <CreateTenderClient />
      </Suspense>
    </Layout>
  );
}
