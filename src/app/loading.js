'use client';

import React from "react";
import styled, { keyframes } from "styled-components";

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
  background-color: #f8f9fa;
`;
LoadingContainer.displayName = "LoadingContainer";

const Spinner = styled.div`
  width: 48px;
  height: 48px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #0066cc;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: 24px;
`;
Spinner.displayName = "Spinner";

const LoadingText = styled.p`
  font-size: 16px;
  color: #666;
  margin: 0;
  animation: ${pulse} 1.5s ease-in-out infinite;
`;
LoadingText.displayName = "LoadingText";

const LoadingDots = styled.span`
  &::after {
    content: '';
    animation: ${pulse} 1.5s ease-in-out infinite;
  }
`;
LoadingDots.displayName = "LoadingDots";

const LoadingPage = () => {
  return (
    <LoadingContainer>
      <Spinner />
      <LoadingText>
        Загрузка<LoadingDots>...</LoadingDots>
      </LoadingText>
    </LoadingContainer>
  );
};

export default LoadingPage;
