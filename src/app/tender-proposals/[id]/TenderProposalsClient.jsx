"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import API_CONFIG from "../../../config/api";
import { useAuth } from "../../../context/AuthContext";

const TenderProposalsContainer = styled.div`
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderProposalsContainer.displayName = "TenderProposalsContainer";

const BackButtonMiniSection = styled.div`
  height: 70px;
  padding: 24px 160px 12px 160px;
`;
BackButtonMiniSection.displayName = "BackButtonMiniSection";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const BackButton = styled.button`
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;
  border: none;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const Title = styled.h1`
  font-size: 42px;
  font-weight: 900;
  line-height: 1.5;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 24px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-size: 24px;
  font-weight: 900;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const TenderInfoCard = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
`;
TenderInfoCard.displayName = "TenderInfoCard";

const TenderTitle = styled.h3`
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
`;
TenderTitle.displayName = "TenderTitle";

const TenderDetail = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;

  strong {
    color: #333;
  }
`;
TenderDetail.displayName = "TenderDetail";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const NoTenderMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoTenderMessage.displayName = "NoTenderMessage";

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
`;
LoadingMessage.displayName = "LoadingMessage";

const ErrorMessage = styled.div`
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #dc3545;
`;
ErrorMessage.displayName = "ErrorMessage";

// Компонент для отображения ценовых предложений
const ProposalCard = styled.div`
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;
ProposalCard.displayName = "ProposalCard";

const ProposalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
`;
ProposalHeader.displayName = "ProposalHeader";

const CompanyName = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
`;
CompanyName.displayName = "CompanyName";

const ProposalDate = styled.span`
  font-size: 14px;
  color: #666;
`;
ProposalDate.displayName = "ProposalDate";

const ProposalDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;
ProposalDetails.displayName = "ProposalDetails";

const ProposalField = styled.div`
  font-size: 14px;
  color: #666;

  strong {
    color: #333;
    font-weight: 600;
  }
`;
ProposalField.displayName = "ProposalField";

const ProposalComment = styled.div`
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #666;
  margin-top: 12px;

  strong {
    color: #333;
  }
`;
ProposalComment.displayName = "ProposalComment";

const TenderProposalsClient = ({ tenderId }) => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [tenderInfo, setTenderInfo] = useState(null);
  const [materials, setMaterials] = useState([]);
  const [proposals, setProposals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const purchReqId = tenderId;

  // Функция для получения информации о тендере
  const fetchTenderInfo = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqTables/${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setTenderInfo(data);
      } else {
        throw new Error("Не удалось загрузить информацию о тендере");
      }
    } catch (error) {
      console.error("Ошибка при загрузке тендера:", error);
      setError(error.message);
    }
  };

  // Функция для получения материалов тендера
  const fetchTenderMaterials = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqLines?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setMaterials(Array.isArray(data) ? data : []);
      } else {
        throw new Error("Не удалось загрузить материалы тендера");
      }
    } catch (error) {
      console.error("Ошибка при загрузке материалов:", error);
      setError(error.message);
    }
  };

  // Функция для получения ценовых предложений
  const fetchProposals = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PurchReqPrices/GetByPurchReqId?purchReqId=${purchReqId}`
      );
      if (response.ok) {
        const data = await response.json();
        setProposals(data);
      } else {
        throw new Error("Не удалось загрузить ценовые предложения");
      }
    } catch (error) {
      console.error("Ошибка при загрузке предложений:", error);
      setError(error.message);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    const loadData = async () => {
      if (!purchReqId) {
        setError("ID тендера не указан");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Сначала пытаемся получить базовую информацию из localStorage
        let tenderBasicInfo = null;
        if (typeof window !== "undefined") {
          try {
            const saved = localStorage.getItem("selectedTenderInfo");
            const tender = saved ? JSON.parse(saved) : null;

            if (tender && tender.PurchReqId.toString() === purchReqId) {
              tenderBasicInfo = tender;
            }
          } catch (error) {
            console.warn("Ошибка при чтении localStorage:", error);
          }
        }

        // Загружаем все данные параллельно
        await Promise.all([
          fetchTenderInfo(),
          fetchTenderMaterials(),
          fetchProposals(),
        ]);
      } catch (err) {
        setError(err.message);
        console.error("Ошибка при загрузке данных:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (purchReqId) {
      loadData();
    }
  }, [purchReqId, isAuthenticated, router]);

  const handleBack = () => {
    router.push("/my-tenders");
  };

  if (isLoading) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <LoadingMessage>Загрузка ценовых предложений...</LoadingMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (error) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <ErrorMessage>Ошибка: {error}</ErrorMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  if (!tenderInfo) {
    return (
      <TenderProposalsContainer>
        <ContentContainer>
          <NoTenderMessage>Тендер не найден</NoTenderMessage>
        </ContentContainer>
      </TenderProposalsContainer>
    );
  }

  return (
    <TenderProposalsContainer>
      <BackButtonMiniSection>
        <BackButton onClick={handleBack}>
          <img src="/icons/back.svg" alt="Назад" />
          Назад к моим тендерам
        </BackButton>
      </BackButtonMiniSection>

      <ContentContainer>
        <Title>Ценовые предложения по тендеру</Title>

        <TenderInfoCard>
          <TenderTitle>{tenderInfo.DocNum}</TenderTitle>
          <TenderDetail>
            <strong>Дата создания:</strong>{" "}
            {new Date(tenderInfo.DocDate).toLocaleDateString("ru-RU")}
          </TenderDetail>
          <TenderDetail>
            <strong>Срок поставки:</strong>{" "}
            {tenderInfo.DeliveryDate
              ? new Date(tenderInfo.DeliveryDate).toLocaleDateString("ru-RU")
              : "Не указан"}
          </TenderDetail>
          <TenderDetail>
            <strong>Адрес доставки:</strong>{" "}
            {tenderInfo.DeliveryAddress || "Не указан"}
          </TenderDetail>
          <TenderDetail>
            <strong>Статус:</strong>{" "}
            {tenderInfo.PurchStatus === 1 ? "Активный" : "Завершен"}
          </TenderDetail>
        </TenderInfoCard>

        <SectionTitle>Материалы тендера</SectionTitle>
        {materials.map((material) => (
          <ProductFormCard key={material.LineNum}>
            <ProductInfo>
              <ProductId>ID: {material.MaterialId}</ProductId>
              <ProductTitle>{material.MaterialName}</ProductTitle>
              <TenderDetail>
                <strong>Количество:</strong> {material.Quantity}{" "}
                {material.UnitId}
              </TenderDetail>
              <TenderDetail>
                <strong>Максимальная цена:</strong>{" "}
                {material.MaxPrice
                  ? `${material.MaxPrice.toLocaleString()} ₸`
                  : "Не указана"}
              </TenderDetail>
            </ProductInfo>
          </ProductFormCard>
        ))}

        <SectionTitle>
          Полученные ценовые предложения ({proposals.length})
        </SectionTitle>
        {proposals.length === 0 ? (
          <NoTenderMessage>
            По данному тендеру пока не поступило ни одного ценового предложения
          </NoTenderMessage>
        ) : (
          proposals.map((proposal, index) => (
            <ProposalCard key={index}>
              <ProposalHeader>
                <CompanyName>
                  {proposal.CompanyName || "Компания не указана"}
                </CompanyName>
                <ProposalDate>
                  {new Date(
                    proposal.CreatedDate || proposal.DocDate
                  ).toLocaleDateString("ru-RU")}
                </ProposalDate>
              </ProposalHeader>

              <ProposalDetails>
                <ProposalField>
                  <strong>Цена за единицу:</strong>{" "}
                  {proposal.UnitPrice
                    ? `${proposal.UnitPrice.toLocaleString()} ₸`
                    : "Не указана"}
                </ProposalField>
                <ProposalField>
                  <strong>Общая сумма:</strong>{" "}
                  {proposal.TotalPrice
                    ? `${proposal.TotalPrice.toLocaleString()} ₸`
                    : "Не указана"}
                </ProposalField>
                <ProposalField>
                  <strong>Срок поставки:</strong>{" "}
                  {proposal.DeliveryDate
                    ? new Date(proposal.DeliveryDate).toLocaleDateString(
                        "ru-RU"
                      )
                    : "Не указан"}
                </ProposalField>
                <ProposalField>
                  <strong>Материал:</strong>{" "}
                  {proposal.MaterialName || "Не указан"}
                </ProposalField>
              </ProposalDetails>

              {proposal.Comment && (
                <ProposalComment>
                  <strong>Комментарий:</strong> {proposal.Comment}
                </ProposalComment>
              )}
            </ProposalCard>
          ))
        )}
      </ContentContainer>
    </TenderProposalsContainer>
  );
};

export default TenderProposalsClient;
