import Layout from "../../components/Layout";
import MyTendersClient from "./MyTendersClient";
import { ISRUtils } from "../../config/isr";

// Генерация мета-тегов для SEO
export async function generateMetadata() {
  return {
    title:
      "Мои тендеры | SADI Shop - интернет-магазин строительных материалов",
    description:
      "Просмотрите свои созданные тендеры на закуп строительных материалов в SADI Shop. Управляйте тендерами, отслеживайте статус и получайте предложения от поставщиков в Казахстане.",
    keywords: [
      "SADI Shop",
      "САДИ Шоп",
      "sadi",
      "сади",
      "мои тендеры",
      "созданные тендеры",
      "управление тендерами",
      "закуп материалов",
      "закупки",
      "строительные материалы",
      "стройка",
      "строительство",
      "тендер строительство",
      "закупка оборудования",
      "тендер инструменты",
      "электронные торги",
      "государственные закупки",
      "коммерческие тендеры",
      "поставщики материалов",
      "Казахстан",
    ],
    openGraph: {
      title: "Мои тендеры",
      description:
        "Просмотрите свои созданные тендеры на закуп строительных материалов, инструментов и оборудования. Управляйте тендерами и отслеживайте предложения.",
      type: "website",
      url: "https://shop.sadi.kz/my-tenders",
      siteName: "SADI.KZ",
      images: [
        {
          url: "/images/tender-og.jpg",
          width: 1200,
          height: 630,
          alt: "Мои тендеры на закуп материалов",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Мои тендеры",
      description:
        "Просмотрите свои созданные тендеры на закуп строительных материалов, инструментов и оборудования.",
      images: ["/images/tender-og.jpg"],
    },
    robots: {
      index: false, // Приватная страница, не индексируем
      follow: true,
    },
  };
}

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Мои тендеры",
  description:
    "Просмотрите свои созданные тендеры на закуп строительных материалов",
  url: "https://shop.sadi.kz/my-tenders",
  isPartOf: {
    "@type": "WebSite",
    name: "SADI Shop",
    url: "https://shop.sadi.kz",
  },
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Мои тендеры",
        item: "https://shop.sadi.kz/my-tenders",
      },
    ],
  },
};

// ISR: Кэширование страницы моих тендеров на 1 час (приватная страница)
export const revalidate = 3600; // 1 час в секундах

// Основной серверный компонент
export default function MyTendersPage() {
  // Логируем ISR операцию
  ISRUtils.logISROperation("my-tenders-page", {
    revalidate: 3600,
    note: "Страница моих тендеров с ISR кэшированием",
  });
  
  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO-контент для поисковых систем */}
      <div style={{ display: "none" }}>
        <h1>Мои тендеры на закуп строительных материалов</h1>
        <h2>Управление созданными тендерами</h2>
        <p>
          На этой странице вы можете просматривать все свои созданные тендеры
          на закуп строительных материалов, инструментов и оборудования.
          Отслеживайте статус тендеров, количество позиций и сроки поставки.
        </p>
        <h3>Возможности управления тендерами</h3>
        <ul>
          <li>Просмотр всех созданных тендеров</li>
          <li>Отслеживание статуса тендеров</li>
          <li>Информация о количестве позиций</li>
          <li>Контроль сроков поставки</li>
        </ul>
      </div>

      <MyTendersClient />
    </Layout>
  );
}
