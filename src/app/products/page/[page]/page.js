import { notFound } from "next/navigation";
import Layout from "../../../../components/Layout";
import PriceList from "../../../../components/PriceList";
import SEOLinks from "../../../../components/SEOLinks";
import ApiService from "../../../../services/api.service";
import { ISRUtils } from "../../../../config/isr";

// ОПЦИОНАЛЬНО: ISR для каталога (если нужно еще больше экономии)
export async function generateStaticParams() {
  try {
    // Предгенерируем только первые 10 страниц каталога
    const pages = [];
    for (let i = 1; i <= 10; i++) {
      pages.push({ page: i.toString() });
    }
    console.log(`ISR Catalog: Предгенерируем ${pages.length} страниц каталога`);
    return pages;
  } catch (error) {
    console.error("ISR Catalog: Ошибка при генерации путей:", error);
    return [];
  }
}

// УМНЫЙ ГИБРИД: СТАТИКА для обычного каталога, SSR для поиска
// Убираем revalidate для страниц БЕЗ query параметров - полная статическая генерация
// Новые товары добавляются в конец, пользователи редко листают дальше 20 страниц

// Умная генерация метаданных в зависимости от наличия поиска
export async function generateMetadata({ params, searchParams }) {
  const page = parseInt(params.page) || 1;
  const { name, code, city } = searchParams || {};

  // Определяем тип рендеринга
  const isSearch = !!(name || code);

  // Логируем тип рендеринга для мониторинга
  ISRUtils.logISROperation("catalog-metadata", {
    page,
    isSearch,
    searchQuery: name,
    categoryCode: code,
    renderType: isSearch ? "SSR (поиск)" : "STATIC (каталог)",
  });

  // ДИНАМИЧЕСКИЕ МЕТА-ТЕГИ для поисковых запросов (SSR)
  if (name) {
    const searchTitle = `${name} купить в SADI Shop - страница ${page}`;
    const searchDescription = `Найдите "${name}" в каталоге SADI Shop - страница ${page}. Лучшие цены на строительные материалы, быстрая доставка по Казахстану. Более 117,000 товаров от проверенных поставщиков.`;

    return {
      title: searchTitle + (city ? ` в ${city}` : ""),
      description: searchDescription + (city ? ` Доставка в ${city}.` : ""),
      keywords: `${name}, купить ${name}, SADI Shop, САДИ Шоп, строительные материалы, стройматериалы, ${
        city || "Казахстан"
      }, интернет-магазин, стройка, строительство, поиск товаров`,
      openGraph: {
        title: searchTitle,
        description: searchDescription,
        url: `https://shop.sadi.kz/products/page/${page}?name=${encodeURIComponent(
          name
        )}`,
        type: "website",
        siteName: "SADI Shop",
        locale: "ru_RU",
      },
      twitter: {
        card: "summary_large_image",
        title: searchTitle,
        description: searchDescription,
      },
      alternates: {
        canonical: `https://shop.sadi.kz/products/page/${page}?name=${encodeURIComponent(
          name
        )}`,
      },
    };
  }

  // ДИНАМИЧЕСКИЕ МЕТА-ТЕГИ для категорий (SSR)
  if (code) {
    const categoryTitle = `Категория ${code} - строительные материалы | SADI Shop`;
    const categoryDescription = `Товары категории ${code} в SADI Shop - интернет-магазине строительных материалов в Казахстане - страница ${page}`;

    return {
      title: categoryTitle + (city ? ` в ${city}` : ""),
      description: categoryDescription + (city ? ` Доставка в ${city}.` : ""),
      keywords: `категория ${code}, SADI Shop, САДИ Шоп, строительные материалы, стройматериалы, ${
        city || "Казахстан"
      }, интернет-магазин, каталог товаров`,
      openGraph: {
        title: categoryTitle,
        description: categoryDescription,
        url: `https://shop.sadi.kz/products/page/${page}?code=${code}`,
        type: "website",
        siteName: "SADI Shop",
        locale: "ru_RU",
      },
      twitter: {
        card: "summary_large_image",
        title: categoryTitle,
        description: categoryDescription,
      },
      alternates: {
        canonical: `https://shop.sadi.kz/products/page/${page}?code=${code}`,
      },
    };
  }

  // СТАТИЧЕСКИЕ МЕТА-ТЕГИ для обычного каталога (ISR)
  const staticTitle = `Каталог строительных материалов - страница ${page} | SADI Shop`;
  const staticDescription = `Каталог строительных материалов SADI Shop - страница ${page}. Более 117,000 товаров от проверенных поставщиков в Казахстане. Лучшие цены, быстрая доставка.`;

  const canonical = `https://shop.sadi.kz/products/page/${page}`;

  return {
    title: staticTitle + (city ? ` в ${city}` : ""),
    description: staticDescription + (city ? ` Доставка в ${city}.` : ""),
    keywords: `SADI Shop, САДИ Шоп, sadi, сади, интернет-магазин строительных материалов, каталог стройматериалов, ${
      city || "Казахстан"
    }, строительные материалы онлайн, стройка, строительство, закупки`,
    openGraph: {
      title: staticTitle,
      description: staticDescription,
      type: "website",
      url: canonical,
      siteName: "SADI Shop",
      locale: "ru_RU",
    },
    twitter: {
      card: "summary_large_image",
      title: staticTitle,
      description: staticDescription,
    },
    alternates: {
      canonical,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// Серверный компонент для получения данных
async function getProductsData(page, searchParams) {
  console.log(
    "SSR: Загружаем данные для страницы",
    page,
    "с параметрами:",
    searchParams
  );

  try {
    const pageSize = 20;
    const offset = (page - 1) * pageSize;

    // Формируем параметры запроса
    const queryParams = {
      offset,
      limit: pageSize,
    };

    // Добавляем фильтры из searchParams
    if (searchParams.name) {
      queryParams.name = searchParams.name;
    }

    if (searchParams.code) {
      queryParams.code = searchParams.code;
    }

    if (searchParams.city) {
      queryParams.city = searchParams.city;
    }

    // Получаем данные с сервера
    const filters = {};
    if (queryParams.name) filters.query = queryParams.name;
    if (queryParams.code) filters.code = queryParams.code;

    const response = await ApiService.getProductsWithPagination(
      page,
      pageSize,
      filters
    );

    // Правильная структура для SSR
    const result = {
      products: response.data || response.products || [],
      totalCount: response.pagination?.totalItems || response.totalCount || 0,
      currentPage: page,
      totalPages:
        response.pagination?.totalPages ||
        Math.ceil(
          (response.pagination?.totalItems || response.totalCount || 0) /
            pageSize
        ),
      filters: {
        name: searchParams.name || null,
        code: searchParams.code || null,
        city: searchParams.city || null,
      },
    };

    console.log("SSR: Результат загрузки данных:", {
      productsCount: result.products.length,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      filters: result.filters,
    });

    return result;
  } catch (error) {
    console.error("SSR: Ошибка при получении данных товаров:", error);

    // Возвращаем пустые данные, но с правильной структурой
    return {
      products: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      filters: {
        name: searchParams.name || null,
        code: searchParams.code || null,
        city: searchParams.city || null,
      },
    };
  }
}

// Основной компонент страницы с умной логикой
export default async function ProductsPage({ params, searchParams }) {
  const page = parseInt(params.page);
  const { name, code } = searchParams || {};

  // Валидация страницы
  if (!page || page < 1) {
    notFound();
  }

  // УМНАЯ ЛОГИКА: определяем тип рендеринга
  const isSearch = !!(name || code);

  // Логируем для мониторинга invocations
  ISRUtils.logISROperation("catalog-page-render", {
    page,
    isSearch,
    searchQuery: name,
    categoryCode: code,
    renderType: isSearch ? "SSR (свежие данные)" : "STATIC (полная статика)",
    timestamp: new Date().toISOString(),
  });

  // Получаем данные на сервере (всегда свежие для поиска, кэшированные для каталога)
  const data = await getProductsData(page, searchParams);

  // Если страница больше общего количества страниц, показываем 404
  if (page > data.totalPages && data.totalPages > 0) {
    notFound();
  }

  // Формируем структурированные данные для поисковых систем
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: `Строительные материалы - страница ${page}`,
    description: `Каталог строительных материалов с пагинацией`,
    url: `https://shop.sadi.kz/products/page/${page}`,
    mainEntity: {
      "@type": "ItemList",
      numberOfItems: data.totalCount,
      itemListElement: data.products.map((product, index) => ({
        "@type": "Product",
        position: (page - 1) * 20 + index + 1,
        name: product.MaterialName,
        description: product.Description || "",
        offers: {
          "@type": "Offer",
          price: product.RetailPrice || 0,
          priceCurrency: "KZT",
          availability: "https://schema.org/InStock",
        },
      })),
    },
  };

  return (
    <Layout>
      {/* Структурированные данные для поисковых систем */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO ссылки для лучшей индексации */}
      <SEOLinks currentPage={data.currentPage} totalPages={data.totalPages} />

      {/* Основной контент */}
      <PriceList initialData={data} isServerRendered={true} />
    </Layout>
  );
}
