import Layout from "../components/Layout";
import Link from "next/link";
import { ISRUtils } from "../config/isr";

// Генерация метаданных для главной страницы
export const metadata = {
  title: "SADI Shop - интернет-магазин строительных материалов в Казахстане",
  description:
    "SADI Shop - интернет-магазин строительных материалов в Казахстане. Более 117,000 товаров от проверенных поставщиков в Астане, Алматы и других городах. Лучшие цены, быстрая доставка.",
  keywords:
    "SADI Shop, САДИ Шоп, sadi, сади, интернет-магазин строительных материалов, стройматериалы Казахстан, shop.sadi.kz, строительные материалы, стройка, строительство, маркетплейс, закупки, тендер, Астана, Алматы, купить, цены, доставка, поставщики",
  openGraph: {
    title: "SADI Shop - интернет-магазин строительных материалов в Казахстане",
    description:
      "SADI Shop - интернет-магазин строительных материалов в Казахстане. Более 117,000 товаров от проверенных поставщиков.",
    type: "website",
    url: "https://shop.sadi.kz",
    siteName: "SADI.KZ",
    locale: "ru_RU",
  },
  twitter: {
    card: "summary_large_image",
    title: "Главная | SADI.KZ - маркетплейс строительных материалов",
    description:
      "SADI.KZ - крупнейший маркетплейс строительных материалов в Казахстане",
  },
  alternates: {
    canonical: "https://shop.sadi.kz",
  },
  robots: {
    index: true,
    follow: true,
  },
};

// Структурированные данные для главной страницы
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  name: "SADI Shop",
  alternateName: ["САДИ Шоп", "shop.sadi.kz"],
  url: "https://shop.sadi.kz",
  description: "Интернет-магазин строительных материалов в Казахстане",
  potentialAction: {
    "@type": "SearchAction",
    target: "https://shop.sadi.kz/products/page/1?name={search_term_string}",
    "query-input": "required name=search_term_string",
  },
  publisher: {
    "@type": "Organization",
    name: "SADI Shop",
    url: "https://shop.sadi.kz",
    logo: "https://shop.sadi.kz/images/placeholder.png",
  },
};

// СТАТИЧЕСКАЯ ГЕНЕРАЦИЯ: Главная страница полностью статическая
// Убираем revalidate для максимальной экономии invocations

// Главная страница с контентом
export default function Home() {
  // Логируем статическую генерацию
  ISRUtils.logISROperation("home-page", {
    type: "STATIC",
    note: "Главная страница - полная статическая генерация",
  });
  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      <div
        style={{
          padding: "40px 20px",
          maxWidth: "1200px",
          margin: "0 auto",
          textAlign: "center",
        }}
      >
        <h1 style={{ fontSize: "2.5rem", marginBottom: "20px", color: "#333" }}>
          SADI Shop - Интернет-магазин строительных материалов
        </h1>

        <p
          style={{
            fontSize: "1.2rem",
            marginBottom: "30px",
            color: "#666",
            maxWidth: "800px",
            margin: "0 auto 30px",
          }}
        >
          Крупнейший маркетплейс строительных материалов в Казахстане. Более
          117,000 товаров от проверенных поставщиков с доставкой по всей стране.
        </p>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
            gap: "30px",
            marginBottom: "40px",
          }}
        >
          <div
            style={{
              padding: "30px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h2 style={{ color: "#0066cc", marginBottom: "15px" }}>
              Широкий ассортимент
            </h2>
            <p>
              Более 117,000 наименований строительных материалов, инструментов и
              оборудования
            </p>
          </div>

          <div
            style={{
              padding: "30px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h2 style={{ color: "#0066cc", marginBottom: "15px" }}>
              Проверенные поставщики
            </h2>
            <p>Работаем только с надежными поставщиками по всему Казахстану</p>
          </div>

          <div
            style={{
              padding: "30px",
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
            }}
          >
            <h2 style={{ color: "#0066cc", marginBottom: "15px" }}>
              Лучшие цены
            </h2>
            <p>Сравнение цен от разных поставщиков для выгодных покупок</p>
          </div>
        </div>

        <div style={{ marginBottom: "40px" }}>
          <Link
            href="/products/page/1"
            style={{
              display: "inline-block",
              padding: "15px 30px",
              backgroundColor: "#0066cc",
              color: "white",
              textDecoration: "none",
              borderRadius: "8px",
              fontSize: "1.1rem",
              fontWeight: "bold",
              marginRight: "20px",
            }}
          >
            Перейти в каталог
          </Link>

          {/* <Link
            href="/create-tender"
            style={{
              display: "inline-block",
              padding: "15px 30px",
              backgroundColor: "#28a745",
              color: "white",
              textDecoration: "none",
              borderRadius: "8px",
              fontSize: "1.1rem",
              fontWeight: "bold",
            }}
          >
            Создать тендер
          </Link> */}
        </div>

        <div
          style={{
            backgroundColor: "#e9ecef",
            padding: "30px",
            borderRadius: "8px",
            textAlign: "left",
          }}
        >
          <h2 style={{ marginBottom: "20px", color: "#333" }}>
            География работы
          </h2>
          <p style={{ marginBottom: "15px" }}>
            SADI Shop работает по всему Казахстану, включая крупные города:
          </p>
          <p style={{ color: "#666" }}>
            Астана, Алматы, Шымкент, Караганда, Актобе, Тараз, Павлодар,
            Усть-Каменогорск, Семей, Атырау, Костанай, Петропавловск, Кызылорда,
            Актау, Талдыкорган и другие регионы Казахстана.
          </p>
        </div>

        <div
          style={{
            marginTop: "40px",
            backgroundColor: "#f8f9fa",
            padding: "30px",
            borderRadius: "8px",
            textAlign: "left",
          }}
        >
          <h2 style={{ marginBottom: "20px", color: "#333" }}>
            SADI Shop - ваш надежный партнер в стройке
          </h2>
          <p style={{ marginBottom: "15px" }}>
            <strong>SADI</strong> (САДИ) - это современная платформа для закупки
            строительных материалов. Наш интернет-магазин предлагает полный
            спектр товаров для стройки и строительства.
          </p>
          <p style={{ color: "#666" }}>
            Создавайте тендеры на закупки, сравнивайте цены поставщиков,
            заказывайте материалы с доставкой. SADI Shop - это удобство,
            качество и экономия для вашего строительного проекта.
          </p>
        </div>
      </div>
    </Layout>
  );
}
