"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { queryClient } from "../config/queryClient";
import { CartProvider } from "../context/CartContext";
import { AuthProvider } from "../context/AuthContext";
import { AveragePricesProvider } from "../context/AveragePricesContext";
import StyledComponentsRegistry from "../lib/registry";

export default function ClientProviders({ children }) {
  return (
    <StyledComponentsRegistry>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CartProvider>
            <AveragePricesProvider>
              {children}
              <ReactQueryDevtools
                initialIsOpen={false}
                position="bottom-right"
              />
            </AveragePricesProvider>
          </CartProvider>
        </AuthProvider>
      </QueryClientProvider>
    </StyledComponentsRegistry>
  );
}
