import Layout from "../../../components/Layout";
import TenderProposalClient from "./TenderProposalClient";

export async function generateMetadata({ params }) {
  const { id } = params;

  return {
    title: `Подача предложения на тендер ${id} | SADI Shop - строительные материалы в Астане, Казахстан`,
    description: `Подайте предложение на тендер ${id}. Укажите цены и условия поставки строительных материалов через SADI Shop в Астане, Казахстан.`,
    keywords:
      "тендер, предложение, строительные материалы, поставка, SADI, Астана, Казахстан",
    openGraph: {
      title: `Подача предложения на тендер ${id} | SADI Shop`,
      description: `Подайте предложение на тендер ${id}. Укажите цены и условия поставки строительных материалов.`,
      type: "website",
      locale: "ru_RU",
    },
  };
}

export default function TenderProposalPage({ params }) {
  return (
    <Layout>
      <TenderProposalClient tenderId={params.id} />
    </Layout>
  );
}
