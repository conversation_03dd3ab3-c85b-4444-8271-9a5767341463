import ISR_CONFIG from "../config/isr";

export default function robots() {
  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/api/",
          "/admin/",
          "/_next/",
          "/private/",
          "/auth",
          "/cart",
          "*.json",
          "*.xml",
          // Агрессивные ограничения для экономии invocations
          "/products/page/[5-9][0-9][0-9]*", // Страницы 500+
          "/products/page/[1-9][0-9][0-9][0-9]*", // Страницы 1000+
          "/products/page/[1-9][0-9][0-9][0-9][0-9]*", // Страницы 10000+
          // Ограничиваем товары с высокими ID
          "/product/[5-9][0-9][0-9][0-9][0-9]*", // Товары с ID 50000+
        ],
        allow: [
          "/create-tender",
          "/find-tender",
          "/tender-form",
          "/about",
          // Разрешаем только первые 500 страниц каталога
          "/products/page/[1-9]",
          "/products/page/[1-9][0-9]",
          "/products/page/[1-4][0-9][0-9]", // До 499
        ],
        crawlDelay: 10, // Увеличиваем задержку до 10 секунд
      },
      {
        userAgent: "Googlebot",
        allow: "/",
        disallow: [
          "/api/",
          "/admin/",
          "/_next/",
          "/private/",
          "/auth",
          "/cart",
          // Для Google тоже ограничиваем для экономии
          "/products/page/[1-9][0-9][0-9][0-9]*", // Страницы 1000+
          "/product/[1-9][0-9][0-9][0-9][0-9]*", // Товары с ID 10000+
        ],
        allow: ["/create-tender", "/find-tender", "/tender-form", "/about"],
        crawlDelay: 5, // Увеличиваем задержку для Google
      },
      {
        userAgent: "YandexBot",
        allow: "/",
        disallow: [
          "/api/",
          "/admin/",
          "/_next/",
          "/private/",
          "/auth",
          "/cart",
          // Для Яндекса более строгие ограничения
          "/products/page/[1-9][0-9][0-9][0-9]*", // Страницы 1000+
        ],
        allow: ["/create-tender", "/find-tender", "/tender-form", "/about"],
        crawlDelay: 8, // Увеличиваем задержку для Яндекса
      },
      {
        userAgent: "bingbot",
        allow: "/",
        disallow: [
          "/api/",
          "/admin/",
          "/_next/",
          "/private/",
          "/auth",
          "/cart",
          "/products/page/[1-9][0-9][0-9][0-9]*", // Страницы 1000+
        ],
        allow: ["/create-tender", "/find-tender", "/tender-form", "/about"],
        crawlDelay: ISR_CONFIG.BOT_OPTIMIZATION.CRAWL_DELAYS.BINGBOT,
      },
    ],
    sitemap: [
      "https://shop.sadi.kz/sitemap-index.xml", // Главный индексный файл (включает все остальные)
    ],
    host: "https://shop.sadi.kz",
  };
}
