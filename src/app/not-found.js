'use client';

import React from "react";
import Link from "next/link";
import styled from "styled-components";
import Layout from "../components/Layout";

const NotFoundContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
  padding: 40px 20px;
  text-align: center;
`;
NotFoundContainer.displayName = "NotFoundContainer";

const NotFoundIcon = styled.div`
  font-size: 120px;
  color: #e0e0e0;
  margin-bottom: 24px;
  font-weight: bold;

  @media (max-width: 768px) {
    font-size: 80px;
    margin-bottom: 16px;
  }
`;
NotFoundIcon.displayName = "NotFoundIcon";

const NotFoundTitle = styled.h1`
  font-size: 48px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 32px;
    margin-bottom: 12px;
  }
`;
NotFoundTitle.displayName = "NotFoundTitle";

const NotFoundMessage = styled.p`
  font-size: 18px;
  color: #666;
  margin-bottom: 32px;
  max-width: 500px;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 16px;
    margin-bottom: 24px;
  }
`;
NotFoundMessage.displayName = "NotFoundMessage";

const ButtonContainer = styled.div`
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    width: 100%;
    max-width: 300px;
  }
`;
ButtonContainer.displayName = "ButtonContainer";

const HomeButton = styled(Link)`
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  transition: background-color 0.2s ease;
  display: inline-block;

  &:hover {
    background-color: #0055b3;
  }
`;
HomeButton.displayName = "HomeButton";

const BackButton = styled.button`
  background-color: white;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #0066cc;
  }
`;
BackButton.displayName = "BackButton";

const SuggestionsList = styled.div`
  margin-top: 40px;
  text-align: left;
  max-width: 400px;

  h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    margin-bottom: 8px;
  }

  a {
    color: #0066cc;
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
`;
SuggestionsList.displayName = "SuggestionsList";

const NotFoundPage = () => {
  const handleGoBack = () => {
    if (typeof window !== 'undefined') {
      window.history.back();
    }
  };

  return (
    <Layout>
      <NotFoundContainer>
        <NotFoundIcon>404</NotFoundIcon>
        <NotFoundTitle>Страница не найдена</NotFoundTitle>
        <NotFoundMessage>
          К сожалению, запрашиваемая страница не существует или была перемещена.
          Проверьте правильность адреса или воспользуйтесь навигацией по сайту.
        </NotFoundMessage>

        <ButtonContainer>
          <HomeButton href="/">На главную</HomeButton>
          <BackButton onClick={handleGoBack}>Назад</BackButton>
        </ButtonContainer>

        <SuggestionsList>
          <h3>Возможно, вас заинтересует:</h3>
          <ul>
            <li>
              <Link href="/">🏠 Главная страница - каталог товаров</Link>
            </li>
            <li>
              <Link href="/cart">🛒 Корзина - ваши выбранные товары</Link>
            </li>
            <li>
              <Link href="/auth">👤 Авторизация - вход в личный кабинет</Link>
            </li>
          </ul>
        </SuggestionsList>
      </NotFoundContainer>
    </Layout>
  );
};

export default NotFoundPage;
