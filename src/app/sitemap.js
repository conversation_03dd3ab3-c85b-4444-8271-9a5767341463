import ISR_CONFIG, { ISRUtils } from "../config/isr";

// Базовый URL сайта
const BASE_URL = "https://shop.sadi.kz";

// Генерация основного sitemap.xml (только самые важные страницы)
export default async function sitemap() {
  const urls = [];

  try {
    // Статические страницы с приоритетами из ISR конфигурации
    urls.push(
      {
        url: BASE_URL,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: ISR_CONFIG.BOT_OPTIMIZATION.PRIORITIES.HOME,
      },
      {
        url: `${BASE_URL}/products/page/1`,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: ISR_CONFIG.BOT_OPTIMIZATION.PRIORITIES.CATALOG_MAIN,
      },
      {
        url: `${BASE_URL}/create-tender`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.8,
      },
      {
        url: `${BASE_URL}/find-tender`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.8,
      },
      {
        url: `${BASE_URL}/tender-form`,
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.7,
      },
      {
        url: `${BASE_URL}/about`,
        lastModified: new Date(),
        changeFrequency: "monthly",
        priority: 0.6,
      }
    );

    // ЭКСТРЕННАЯ МЕРА: Убираем все динамические страницы из основного sitemap
    // Они будут доступны через sitemap-pages.xml
    // const maxMainSitemapPages = 0; // Отключаем добавление страниц каталога

    ISRUtils.logISROperation("main-sitemap", {
      staticPages: 6, // Добавлена страница find-tender
      catalogPages: 0, // Отключены для экономии
      totalUrls: urls.length,
    });
  } catch (error) {
    console.error("Ошибка при генерации sitemap:", error);
  }

  return urls;
}
