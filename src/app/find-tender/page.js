import { Metadata } from "next";
import Layout from "../../components/Layout";
import FindTenderClient from "./FindTenderClient";
import { ISRUtils } from "../../config/isr";

// SEO мета-теги для страницы поиска тендеров
export const metadata = {
  title:
    "Найти тендер | SADI Shop - поиск тендеров на строительные материалы в Астане, Казахстан",
  description:
    "Находите тендеры на поставку строительных материалов, оборудования и инструментов. Удобный поиск по городам и категориям товаров в SADI Shop.",
  keywords:
    "найти тендер, тендеры строительные материалы, поиск тендеров, закупки материалов, тендеры Казахстан, SADI",
  openGraph: {
    title:
      "Найти тендер | SADI Shop - поиск тендеров на строительные материалы",
    description:
      "Находите тендеры на поставку строительных материалов, оборудования и инструментов. Удобный поиск по городам и категориям товаров.",
    url: "https://shop.sadi.kz/find-tender",
    siteName: "SADI Shop",
    locale: "ru_KZ",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Найти тендер | SADI Shop",
    description:
      "Находите тендеры на поставку строительных материалов, оборудования и инструментов.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://shop.sadi.kz/find-tender",
  },
};

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Найти тендер - SADI Shop",
  description:
    "Поиск тендеров на поставку строительных материалов, оборудования и инструментов",
  url: "https://shop.sadi.kz/find-tender",
  mainEntity: {
    "@type": "SearchAction",
    target: {
      "@type": "EntryPoint",
      urlTemplate: "https://shop.sadi.kz/find-tender?q={search_term_string}",
    },
    "query-input": "required name=search_term_string",
  },
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Найти тендер",
        item: "https://shop.sadi.kz/find-tender",
      },
    ],
  },
  publisher: {
    "@type": "Organization",
    name: "SADI Shop",
    url: "https://shop.sadi.kz",
  },
  inLanguage: "ru-KZ",
  isPartOf: {
    "@type": "WebSite",
    name: "SADI Shop",
    url: "https://shop.sadi.kz",
  },
};

// ISR: Полная статическая генерация без revalidate для SEO
// Страница поиска тендеров - статический контент, интерактивность через клиентские компоненты
export default function FindTenderPage() {
  // Логируем статическую генерацию
  ISRUtils.logISROperation("find-tender-page", {
    type: "STATIC",
    note: "Страница поиска тендеров - полная статическая генерация без revalidate",
    fallback: "blocking",
  });

  return (
    <Layout>
      {/* Структурированные данные для поисковых систем */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Хлебные крошки для SEO */}

      {/* Клиентский компонент для интерактивности */}
      <FindTenderClient />
    </Layout>
  );
}
