// Конфигурация ISR для оптимизации Vercel invocations

export const ISR_CONFIG = {
  // Время ревалидации для разных типов страниц (в секундах)
  REVALIDATE_TIMES: {
    // Товарные страницы - обновляем раз в 2 недели (популярные товары)
    PRODUCT_POPULAR: 14 * 24 * 60 * 60, // 14 дней
    // Обычные товары - обновляем раз в месяц
    PRODUCT_REGULAR: 30 * 24 * 60 * 60, // 30 дней
    // Каталог - обновляем раз в день
    CATALOG: 24 * 60 * 60, // 24 часа
    // Категории - обновляем раз в неделю
    CATEGORY: 7 * 24 * 60 * 60, // 7 дней
    // Главная страница - обновляем каждые 6 часов
    HOME: 6 * 60 * 60, // 6 часов
  },

  // Лимиты для предгенерации (агрессивно уменьшены для экономии)
  STATIC_GENERATION_LIMITS: {
    // Количество товаров для предгенерации при билде
    PRODUCTS_PREBUILD: 50, // Уменьшено с 100 до 50
    // Количество страниц каталога для предгенерации
    CATALOG_PAGES_PREBUILD: 5, // Уменьшено с 10 до 5
    // Количество категорий для предгенерации
    CATEGORIES_PREBUILD: 30, // Уменьшено с 50 до 30
  },

  // Настройки для оптимизации ботов
  BOT_OPTIMIZATION: {
    // Задержка между запросами для разных ботов (в секундах)
    CRAWL_DELAYS: {
      GOOGLEBOT: 1,
      YANDEX: 2,
      BINGBOT: 1,
      DEFAULT: 3,
    },

    // Максимальное количество страниц в одном sitemap (агрессивно уменьшено)
    MAX_SITEMAP_URLS: 2000, // Уменьшено с 10000 до 2000

    // Приоритеты для разных типов страниц
    PRIORITIES: {
      HOME: 1.0,
      CATALOG_MAIN: 0.9,
      CATEGORY_MAIN: 0.8,
      PRODUCT_POPULAR: 0.7,
      PRODUCT_REGULAR: 0.5,
      CATALOG_PAGES: 0.4,
    },
  },

  // Функция для определения приоритета товара в sitemap
  getProductPriority: (productId, index = 0) => {
    // Первые 1000 товаров - высокий приоритет
    if (parseInt(productId) <= 1000 || index < 1000) {
      return ISR_CONFIG.BOT_OPTIMIZATION.PRIORITIES.PRODUCT_POPULAR;
    }
    return ISR_CONFIG.BOT_OPTIMIZATION.PRIORITIES.PRODUCT_REGULAR;
  },

  // Функция для определения частоты обновления в sitemap
  getChangeFrequency: (pageType, index = 0) => {
    switch (pageType) {
      case "home":
        return "daily";
      case "catalog":
        return index < 10 ? "daily" : "weekly";
      case "category":
        return "weekly";
      case "product":
        return index < 1000 ? "weekly" : "monthly";
      default:
        return "monthly";
    }
  },
};

// Утилиты для работы с ISR
export const ISRUtils = {
  // Логирование ISR операций для мониторинга
  logISROperation: (operation, details) => {
    console.log(`🔄 ISR ${operation}:`, {
      ...details,
      timestamp: new Date().toISOString(),
    });
  },
};

export default ISR_CONFIG;
