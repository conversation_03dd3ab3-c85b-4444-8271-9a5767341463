// SEO конфигурация для SADI Shop
export const BRAND_VARIATIONS = {
  // Основные варианты написания
  primary: "SADI Shop",
  variations: [
    "SADI Shop",
    "САДИ Шоп",
    "sadi shop",
    "сади шоп",
    "SADI интернет-магазин",
    "САДИ интернет-магазин",
    "shop.sadi.kz",
    "SADI KZ Shop",
    "САДИ КЗ Шоп",
  ],

  // Для использования в мета-тегах
  keywordsString:
    "SADI Shop, САДИ Шоп, sadi shop, сади шоп, интернет-магазин строительных материалов, shop.sadi.kz",

  // Для описаний
  descriptionVariant: "SADI Shop",

  // Альтернативные домены (если есть)
  domains: ["shop.sadi.kz", "sadi.kz"],
};

// Базовые SEO шаблоны
export const SEO_TEMPLATES = {
  // Шаблон для заголовков страниц
  pageTitle: (pageName, city = "") => {
    const cityPart = city ? ` в ${city}` : " в Казахстане";
    return `${pageName} | SADI Shop - интернет-магазин строительных материалов${cityPart}`;
  },

  // Шаблон для описаний
  pageDescription: (content, city = "") => {
    const cityPart = city ? ` в ${city}` : " в Казахстане";
    return `${content} в SADI Shop - интернет-магазине строительных материалов${cityPart}. Более 117,000 товаров, лучшие цены, быстрая доставка по всему Казахстану.`;
  },

  // Ключевые слова для разных типов страниц
  keywords: {
    main: `${BRAND_VARIATIONS.keywordsString}, строительные материалы, стройматериалы, стройка, строительство, маркетплейс, Казахстан, Астана, Алматы`,
    catalog: `${BRAND_VARIATIONS.keywordsString}, каталог, строительные материалы, стройматериалы, стройка, купить`,
    product: `${BRAND_VARIATIONS.keywordsString}, купить, цена, поставщики, строительные материалы, стройка`,
    tender: `${BRAND_VARIATIONS.keywordsString}, тендер, закуп, закупки, электронные торги, строительство, стройка`,
  },
};

// Города для SEO
export const CITIES = [
  "Астана",
  "Алматы",
  "Шымкент",
  "Караганда",
  "Актобе",
  "Тараз",
  "Павлодар",
  "Усть-Каменогорск",
  "Семей",
  "Атырау",
  "Костанай",
  "Кызылорда",
  "Уральск",
  "Петропавловск",
  "Актау",
];

// Функция для генерации ключевых слов с городами
export const generateCityKeywords = (
  baseKeywords,
  cities = CITIES.slice(0, 5)
) => {
  const cityKeywords = cities.flatMap((city) => [
    `строительные материалы ${city}`,
    `стройматериалы ${city}`,
    `купить ${city}`,
    `поставщики ${city}`,
    `SADI KZ ${city}`,
    `сади кз ${city}`,
  ]);

  return `${baseKeywords}, ${cityKeywords.join(", ")}`;
};

export default {
  BRAND_VARIATIONS,
  SEO_TEMPLATES,
  CITIES,
  generateCityKeywords,
};
