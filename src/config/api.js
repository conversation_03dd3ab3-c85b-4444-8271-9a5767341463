// Переключатель API: false - тестовый API, true - основной API
const USE_PRODUCTION_API = false;

// Конфигурация для тестового API
const TEST_API_CONFIG = {
  BASE_URL: "http://test.api.sadi.kz",
  TOKEN_URL: "http://test.api.sadi.kz/Token",
  AUTH_CREDENTIALS: {
    grant_type: "password",
    username: "",
    password: "",
  },
  TOKEN_LIFETIME_DAYS: 14,
};

// Конфигурация для основного API
const PRODUCTION_API_CONFIG = {
  BASE_URL: "https://api.sadi.kz",
  TOKEN_URL: "https://api.sadi.kz/Token",
  AUTH_CREDENTIALS: {
    grant_type: "password",
    username: "",
    password: "",
  },
  TOKEN_LIFETIME_DAYS: 14,
};

// Выбираем конфигурацию в зависимости от переключателя
const CURRENT_CONFIG = USE_PRODUCTION_API
  ? PRODUCTION_API_CONFIG
  : TEST_API_CONFIG;

// Основная конфигурация API
const API_CONFIG = {
  // Переключатель API
  USE_PRODUCTION_API,

  // Базовый URL API
  BASE_URL: CURRENT_CONFIG.BASE_URL,

  // Пути к API endpoints
  ENDPOINTS: {
    PRODUCTS: "/api/Materials",
    PRODUCT_DETAILS: "/api/Materials/:id",
    CATALOG: "/api/MaterialTrees",
  },

  // URL для получения токена
  TOKEN_URL: CURRENT_CONFIG.TOKEN_URL,

  // Данные для авторизации
  AUTH_CREDENTIALS: CURRENT_CONFIG.AUTH_CREDENTIALS,

  // Время жизни токена в днях
  TOKEN_LIFETIME_DAYS: CURRENT_CONFIG.TOKEN_LIFETIME_DAYS,

  // Настройки пагинации
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
  },

  // Настройки кеширования
  CACHE: {
    STALE_TIME: 5 * 60 * 1000, // 5 минут
    CACHE_TIME: 10 * 60 * 1000, // 10 минут
  },

  // Настройки поиска
  SEARCH: {
    MIN_QUERY_LENGTH: 2,
    DEBOUNCE_DELAY: 300,
  },

  // Настройки изображений
  IMAGES: {
    PLACEHOLDER: "/images/placeholder.png",
  },

  // Настройки отладки
  DEBUG: {
    ENABLED: true, // Включить отладку авторизации
    LOG_TOKENS: true, // Логировать операции с токенами
    LOG_API_CALLS: false, // Логировать API вызовы
  },
};

// Утилиты для отладки авторизации
export const AuthDebug = {
  // Логирование получения токена
  logTokenReceived: (token, expiresIn) => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    const expiryDate = new Date(Date.now() + expiresIn * 1000);
    console.log("🔐 AUTH DEBUG: Токен получен", {
      tokenLength: token ? token.length : 0,
      tokenPreview: token ? `${token.substring(0, 20)}...` : "null",
      expiresIn: `${expiresIn} секунд`,
      expiryDate: expiryDate.toLocaleString(),
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование сохранения токена
  logTokenSaved: (token, storageType = "localStorage") => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log(`💾 AUTH DEBUG: Токен успешно сохранен в ${storageType}`, {
      tokenLength: token ? token.length : 0,
      tokenPreview: token ? `${token.substring(0, 20)}...` : "null",
      storageType,
      storageKey: "api_access_token",
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование загрузки токена
  logTokenLoaded: (token) => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    if (token) {
      console.log("📥 AUTH DEBUG: Токен загружен из localStorage", {
        tokenLength: token.length,
        tokenPreview: `${token.substring(0, 20)}...`,
        timestamp: new Date().toLocaleString(),
      });
    } else {
      console.log("📥 AUTH DEBUG: Токен не найден в localStorage", {
        timestamp: new Date().toLocaleString(),
      });
    }
  },

  // Логирование очистки токена
  logTokenCleared: () => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log("🗑️ AUTH DEBUG: Токен очищен из localStorage", {
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование проверки токена
  logTokenValidation: (isValid, reason) => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    if (isValid) {
      console.log("✅ AUTH DEBUG: Токен валиден", {
        timestamp: new Date().toLocaleString(),
      });
    } else {
      console.log("❌ AUTH DEBUG: Токен невалиден", {
        reason,
        timestamp: new Date().toLocaleString(),
      });
    }
  },

  // Логирование ошибок авторизации
  logAuthError: (error, context) => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.error("🚨 AUTH DEBUG: Ошибка авторизации", {
      context,
      error: error.message || error,
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование попытки авторизации
  logAuthAttempt: (credentials) => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log("🔄 AUTH DEBUG: Попытка авторизации", {
      username: credentials.username || "не указан",
      hasPassword: !!credentials.password,
      grantType: credentials.grant_type,
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование использования токена в запросах
  logTokenUsage: (url, method = "GET") => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log("🔑 AUTH DEBUG: Использование токена в запросе", {
      url,
      method,
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование истечения токена
  logTokenExpired: () => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log("⏰ AUTH DEBUG: Токен истек", {
      timestamp: new Date().toLocaleString(),
    });
  },

  // Логирование обновления токена
  logTokenRefresh: () => {
    if (!API_CONFIG.DEBUG.ENABLED || !API_CONFIG.DEBUG.LOG_TOKENS) return;

    console.log("🔄 AUTH DEBUG: Обновление токена", {
      timestamp: new Date().toLocaleString(),
    });
  },
};

export default API_CONFIG;
