import { QueryClient } from "@tanstack/react-query";

// Создаем клиент React Query с настройками
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Время, в течение которого данные считаются свежими (5 минут)
      staleTime: 5 * 60 * 1000,
      // Время кеширования данных (10 минут)
      cacheTime: 10 * 60 * 1000,
      // Повторные запросы при ошибках
      retry: 3,
      // Интервал между повторными запросами
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Не делать запрос при фокусе окна
      refetchOnWindowFocus: false,
      // Не делать запрос при переподключении
      refetchOnReconnect: true,
    },
    mutations: {
      // Повторные попытки для мутаций
      retry: 1,
    },
  },
});
