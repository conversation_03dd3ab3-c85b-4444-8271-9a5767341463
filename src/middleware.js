import { NextResponse } from 'next/server';

export function middleware(request) {
  const response = NextResponse.next();

  // Добавляем кэширование для статических ресурсов и API
  if (request.nextUrl.pathname.startsWith('/api/') || 
      request.nextUrl.pathname.includes('sitemap') ||
      request.nextUrl.pathname.includes('robots')) {
    
    // Кэшируем sitemap и robots на 1 час
    if (request.nextUrl.pathname.includes('sitemap') || 
        request.nextUrl.pathname.includes('robots')) {
      response.headers.set(
        'Cache-Control',
        'public, max-age=3600, s-maxage=3600'
      );
    }
    
    // Добавляем заголовки для экономии bandwidth
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
  }

  // Для страниц товаров добавляем ISR заголовки
  if (request.nextUrl.pathname.startsWith('/product/')) {
    response.headers.set(
      'Cache-Control',
      'public, max-age=86400, s-maxage=604800' // 1 день для браузера, 7 дней для CDN
    );
  }

  return response;
}

export const config = {
  matcher: [
    '/api/:path*',
    '/product/:path*',
    '/sitemap:path*',
    '/robots.txt'
  ]
};
