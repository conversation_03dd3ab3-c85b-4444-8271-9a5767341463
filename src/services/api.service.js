import API_CONFIG, { AuthDebug } from "../config/api.js";
import authService from "./auth.service.js";

// Функция для получения текущей даты в формате YYYY.MM.DD
const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  return `${year}.${month}.${day}`;
};

// Кэш для хранения данных с TTL
const cache = {
  catalog: { data: null, timestamp: null, ttl: 30 * 60 * 1000 }, // 30 минут
  products: { data: null, timestamp: null, ttl: 10 * 60 * 1000 }, // 10 минут
  paginatedProducts: {}, // Кэш для пагинированных данных
};

// Функция проверки актуальности кэша
const isCacheValid = (cacheItem) => {
  if (!cacheItem.data || !cacheItem.timestamp) return false;
  return Date.now() - cacheItem.timestamp < cacheItem.ttl;
};

// Функция установки данных в кэш
const setCacheData = (cacheKey, data) => {
  if (cache[cacheKey]) {
    cache[cacheKey].data = data;
    cache[cacheKey].timestamp = Date.now();
  }
};

/**
 * Базовая функция для выполнения запросов к API с кэшированием и авторизацией
 * @param {string} url - URL для запроса
 * @param {Object} options - Опции запроса
 * @param {string} cacheKey - Ключ для кэширования результата
 * @returns {Promise<any>} - Результат запроса
 */
const fetchApi = async (url, options = {}, cacheKey = null) => {
  // Если указан ключ кэша и данные актуальны, возвращаем их
  if (cacheKey && cache[cacheKey] && isCacheValid(cache[cacheKey])) {
    console.log(`Данные получены из кэша: ${cacheKey}`);
    return cache[cacheKey].data;
  }

  try {
    console.log(`Запрос к API: ${url}`);

    // Получаем заголовки авторизации
    const authHeaders = await authService.getAuthHeaders();

    // Логируем использование токена в запросе
    if (authHeaders.Authorization) {
      AuthDebug.logTokenUsage(url, options.method || "GET");
    }

    // Объединяем заголовки
    const headers = {
      "Content-Type": "application/json",
      ...authHeaders,
      ...options.headers,
    };

    const response = await fetch(url, {
      method: "GET",
      ...options,
      headers,
    });

    if (!response.ok) {
      // Если ошибка авторизации, очищаем токен
      if (response.status === 401) {
        AuthDebug.logTokenExpired();
        console.warn("⚠️ Ошибка авторизации, очищаем токен");
        authService.clearToken();
      }
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    // Если указан ключ кэша, сохраняем данные в кэш
    if (cacheKey) {
      setCacheData(cacheKey, data);
      console.log(`Данные сохранены в кэш: ${cacheKey}`);
    }

    return data;
  } catch (error) {
    console.error("API request error:", error);
    throw error;
  }
};

/**
 * Сервис для работы с API
 */
const ApiService = {
  /**
   * Получить список продуктов с пагинацией
   * @param {number} page - Номер страницы (начиная с 1)
   * @param {number} pageSize - Размер страницы
   * @param {Object} filters - Фильтры для запроса
   * @param {string} filters.code - Код категории
   * @param {string} filters.query - Поисковый запрос (передается как параметр name в API)
   * @returns {Promise<Object>} - Объект с данными пагинации и списком продуктов
   */
  getProductsWithPagination: async (page = 1, pageSize = 20, filters = {}) => {
    // Используем серверную пагинацию
    const { code, query } = filters || {};

    // Создаем ключ кэша с учетом всех параметров
    const filterKey = `${code || ""}_${query || ""}`;
    const paginationKey = `page_${page}_size_${pageSize}_filters_${filterKey}`;

    // Проверяем кэш для пагинированных данных
    if (cache.paginatedProducts[paginationKey]) {
      console.log(`Пагинированные данные получены из кэша: ${paginationKey}`);
      return cache.paginatedProducts[paginationKey];
    }

    try {
      // Вычисляем offset для API (offset начинается с 0)
      const offset = (page - 1) * pageSize;

      // Формируем URL с параметрами пагинации и фильтрации
      const params = new URLSearchParams({
        offset: offset.toString(),
        limit: pageSize.toString(),
      });

      // Добавляем параметры фильтрации, если они есть
      if (code) {
        params.append("code", code);
      }
      if (query) {
        params.append("name", query);
      }

      const url = `${API_CONFIG.BASE_URL}${
        API_CONFIG.ENDPOINTS.PRODUCTS
      }/?${params.toString()}`;

      console.log(`Запрос к API с пагинацией и фильтрами: ${url}`);

      // Получаем заголовки авторизации
      let authHeaders = {};
      try {
        authHeaders = await authService.getAuthHeaders();
      } catch (error) {
        console.warn(
          "Ошибка при получении заголовков авторизации (SSR):",
          error
        );
        // На сервере авторизация может не работать, продолжаем без токена
      }

      // Объединяем заголовки
      const headers = {
        "Content-Type": "application/json",
        ...authHeaders,
      };

      // Делаем запрос к API
      const response = await fetch(url, {
        method: "GET",
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает просто массив продуктов
      let products, totalItems, totalPages;

      if (Array.isArray(data)) {
        products = data;
        // Если API не возвращает информацию о пагинации, вычисляем приблизительно
        totalItems = products.length;
        totalPages = Math.ceil(totalItems / pageSize);

        // Если получили полную страницу, предполагаем что есть еще данные
        if (products.length === pageSize) {
          totalItems = page * pageSize + 1; // Минимальная оценка
          totalPages = page + 1; // Минимальная оценка
        }
      } else if (data.data && Array.isArray(data.data)) {
        // Если API возвращает объект с данными и пагинацией
        products = data.data;
        totalItems = data.totalItems || data.total || products.length;
        totalPages = data.totalPages || Math.ceil(totalItems / pageSize);
      } else {
        products = [];
        totalItems = 0;
        totalPages = 1;
      }

      // Формируем результат
      const result = {
        data: products,
        pagination: {
          page,
          pageSize,
          totalItems,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };

      // Сохраняем результат в кэш
      cache.paginatedProducts[paginationKey] = result;

      console.log(`Получено ${products.length} продуктов для страницы ${page}`);

      return result;
    } catch (error) {
      console.error("Ошибка при получении продуктов с пагинацией:", error);
      throw error;
    }
  },

  /**
   * Получить детальную информацию о продукте по ID
   * @param {number|string} id - ID продукта
   * @returns {Promise<Object>} - Информация о продукте
   */
  getProductById: async (id) => {
    // Проверяем, есть ли продукт в кэше всех продуктов
    if (cache.products && isCacheValid(cache.products) && cache.products.data) {
      const product = cache.products.data.find(
        (p) => p.MaterialId.toString() === id.toString()
      );
      if (product) {
        console.log(`Продукт ${id} найден в кэше`);
        return product;
      }
    }

    const url = `${
      API_CONFIG.BASE_URL
    }${API_CONFIG.ENDPOINTS.PRODUCT_DETAILS.replace(":id", id)}`;
    return fetchApi(url);
  },

  /**
   * Получить структуру каталога
   * @returns {Promise<Object>} - Структура каталога
   */
  getCatalog: async () => {
    // Используем кэширование для каталога
    return fetchApi(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CATALOG}`,
      {},
      "catalog"
    );
  },

  /**
   * Получить поставщиков для материала
   * @param {number} materialId - ID материала
   * @returns {Promise<Array>} - Массив поставщиков
   */
  getSuppliers: async (materialId) => {
    if (!materialId) {
      return [];
    }

    const fromDate = "2000.01.01";
    const tillDate = getCurrentDate();

    const url = `${API_CONFIG.BASE_URL}/api/Adverts/Suppliers?materialIds=${materialId}&fromDate=${fromDate}&tillDate=${tillDate}`;

    try {
      console.log(`Запрос поставщиков для материала ${materialId}: ${url}`);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data || [];
    } catch (error) {
      console.error("Ошибка при получении поставщиков:", error);
      return [];
    }
  },

  /**
   * Получить средние цены для материалов
   * @param {Array<number>} materialIds - Массив ID материалов
   * @returns {Promise<Array>} - Массив объектов с ценами
   */
  getAveragePrices: async (materialIds) => {
    if (!materialIds || materialIds.length === 0) {
      return [];
    }

    try {
      // Формируем URL с параметрами materialIds
      const params = new URLSearchParams();

      // Добавляем каждый materialId как отдельный параметр
      materialIds.forEach((id) => {
        params.append("materialIds", id);
      });

      // Добавляем константные даты
      params.append("fromDate", "2000.01.01");
      params.append("tillDate", getCurrentDate());

      const url = `${
        API_CONFIG.BASE_URL
      }/api/Adverts/AveragePrices?${params.toString()}`;

      console.log(
        `Запрос средних цен для ${materialIds.length} материалов: ${url}`
      );

      // Получаем заголовки авторизации
      const authHeaders = await authService.getAuthHeaders();

      // Объединяем заголовки
      const headers = {
        "Content-Type": "application/json",
        ...authHeaders,
      };

      // Делаем запрос к API
      const response = await fetch(url, {
        method: "GET",
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает массив объектов с ценами
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error("Ошибка при получении средних цен:", error);
      throw error;
    }
  },

  /**
   * Получить файлы для материалов (новый API)
   * @param {Array<number>} materialIds - Массив ID материалов
   * @returns {Promise<Object>} - Объект где ключи - materialId, значения - массив файлов
   */
  getMaterialFiles: async (materialIds) => {
    if (!materialIds || materialIds.length === 0) {
      return {};
    }

    try {
      // Формируем URL с параметрами materialIds
      const params = new URLSearchParams();

      // Добавляем каждый materialId как отдельный параметр
      materialIds.forEach((id) => {
        params.append("materialIds", id);
      });

      // Добавляем типы файлов (константа)
      params.append("fileType", ".png,.jpg,.jpeg");

      const url = `${
        API_CONFIG.BASE_URL
      }/api/Adverts/Files/ByMaterial?${params.toString()}`;

      console.log(`Запрос файлов для ${materialIds.length} материалов: ${url}`);

      // Делаем запрос к API (без авторизации)
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // API возвращает объект где ключи - materialId, значения - массив файлов
      return data || {};
    } catch (error) {
      console.error("Ошибка при получении файлов материалов:", error);
      throw error;
    }
  },
};

export default ApiService;
