import API_CONFIG, { AuthDebug } from "../config/api.js";

/**
 * Сервис для работы с авторизацией и токенами
 */
class AuthService {
  constructor() {
    this.currentToken = null;
  }

  /**
   * Получить токен доступа
   * @returns {Promise<string|null>} - Токен доступа или null
   */
  async getAccessToken() {
    // Если есть токен в памяти, используем его
    if (this.currentToken) {
      AuthDebug.logTokenLoaded(this.currentToken);
      return this.currentToken;
    }

    // Получаем новый токен
    AuthDebug.logTokenRefresh();
    return await this.requestNewToken();
  }

  /**
   * Запросить новый токен с сервера
   * @returns {Promise<string|null>} - Новый токен или null при ошибке
   */
  async requestNewToken() {
    try {
      if (!API_CONFIG.TOKEN_URL || !API_CONFIG.AUTH_CREDENTIALS) {
        const error = new Error("Не настроены параметры авторизации");
        AuthDebug.logAuthError(error, "requestNewToken - конфигурация");
        throw error;
      }

      // Логируем попытку авторизации
      AuthDebug.logAuthAttempt(API_CONFIG.AUTH_CREDENTIALS);

      // Подготавливаем данные для запроса токена
      const formData = new URLSearchParams();
      Object.entries(API_CONFIG.AUTH_CREDENTIALS).forEach(([key, value]) => {
        formData.append(key, value);
      });

      console.log(`🔄 Запрос токена: ${API_CONFIG.TOKEN_URL}`);

      const response = await fetch(API_CONFIG.TOKEN_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData,
      });

      if (!response.ok) {
        // Пытаемся получить детальную информацию об ошибке
        let errorMessage = `HTTP error! status: ${response.status}`;

        try {
          const errorData = await response.json();
          if (errorData.error_description) {
            errorMessage = errorData.error_description;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.Message) {
            errorMessage = errorData.Message;
          }
        } catch (parseError) {
          // Если не удалось распарсить JSON, используем статус код
          console.warn("Не удалось распарсить ошибку авторизации:", parseError);
        }

        const error = new Error(errorMessage);
        AuthDebug.logAuthError(error, "requestNewToken - HTTP ошибка");
        throw error;
      }

      const data = await response.json();

      if (!data.access_token) {
        let errorMessage = "Токен не получен от сервера";

        // Проверяем, есть ли дополнительная информация об ошибке
        if (data.error_description) {
          errorMessage = data.error_description;
        } else if (data.error) {
          errorMessage = data.error;
        }

        const error = new Error(errorMessage);
        AuthDebug.logAuthError(
          error,
          "requestNewToken - отсутствует access_token"
        );
        throw error;
      }

      // Логируем получение токена
      AuthDebug.logTokenReceived(data.access_token, data.expires_in || 3600);

      // Сохраняем токен в памяти
      this.currentToken = data.access_token;

      console.log("✅ Токен успешно получен и сохранен в памяти");
      return data.access_token;
    } catch (error) {
      AuthDebug.logAuthError(error, "requestNewToken - общая ошибка");
      this.clearToken();
      return null;
    }
  }

  /**
   * Установить токен в памяти
   * @param {string} token - Токен для установки
   */
  setToken(token) {
    this.currentToken = token;
    AuthDebug.logTokenSaved(token);
    console.log("✅ Токен установлен в памяти");
  }

  /**
   * Очистить токен из памяти
   */
  clearToken() {
    this.currentToken = null;
    AuthDebug.logTokenCleared();
    console.log("🗑️ Токен очищен из памяти");
  }

  /**
   * Получить заголовки авторизации для запросов
   * @returns {Promise<Object>} - Объект с заголовками
   */
  async getAuthHeaders() {
    // Если токен есть в памяти, используем его
    if (this.currentToken) {
      AuthDebug.logTokenUsage("getAuthHeaders", "HEADER");
      return {
        Authorization: `Bearer ${this.currentToken}`,
      };
    }

    // Если токена нет, возвращаем пустые заголовки (не запрашиваем автоматически)
    AuthDebug.logTokenValidation(false, "Токен отсутствует в памяти");
    return {};
  }

  /**
   * Получить информацию о текущем состоянии авторизации
   * @returns {Object} - Информация о состоянии авторизации
   */
  getAuthStatus() {
    const hasToken = !!this.currentToken;

    return {
      authenticated: hasToken,
      hasToken: hasToken,
      tokenValid: hasToken,
      message: hasToken ? "Авторизован" : "Токен отсутствует",
    };
  }
}

// Создаем единственный экземпляр сервиса
const authService = new AuthService();

export default authService;
