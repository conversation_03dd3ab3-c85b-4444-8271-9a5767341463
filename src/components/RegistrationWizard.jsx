"use client";

import React, { useState } from "react";
import styled from "styled-components";
import { useAuth } from "../context/AuthContext";
import {
  ActivityTypesStep,
  PortfolioStep,
  CertificatesStep,
  CompanyStoryStep,
} from "./RegistrationWizardSteps";

// Styled Components
const WizardContainer = styled.div`
  background: white;
  border-radius: 12px;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  min-height: 600px;

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 8px;
    max-width: 100%;
  }
`;

const WizardHeader = styled.div`
  margin-bottom: 32px;
`;

const WizardTitle = styled.h2`
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 24px;
  text-align: left;
`;

const ProgressContainer = styled.div`
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
`;

const ProgressText = styled.p`
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
`;

const ProgressBarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background-color: #28a745;
  width: ${(props) => props.percentage}%;
  transition: width 0.3s ease;
`;

const ProgressPercentage = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
`;

const StepContent = styled.div`
  margin-bottom: 32px;
`;

const StepTitle = styled.h3`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
`;

const Input = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const RadioOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${(props) => (props.selected ? "#f0f8ff" : "white")};
  border-color: ${(props) => (props.selected ? "#0066cc" : "#e9ecef")};

  &:hover {
    border-color: #0066cc;
  }
`;

const RadioLabel = styled.span`
  font-size: 16px;
  color: #333;
  font-weight: 500;
`;

const RadioButton = styled.div`
  width: 24px;
  height: 24px;
  border: 2px solid ${(props) => (props.selected ? "#0066cc" : "#e9ecef")};
  border-radius: 4px;
  background-color: ${(props) => (props.selected ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &::after {
    content: "✓";
    color: white;
    font-size: 14px;
    font-weight: bold;
    opacity: ${(props) => (props.selected ? 1 : 0)};
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-top: auto;
`;

const Button = styled.button`
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
`;

const PrimaryButton = styled(Button)`
  background-color: #0066cc;
  color: white;

  &:hover:not(:disabled) {
    background-color: #0055b3;
  }

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #f8f9fa;
  color: #0066cc;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 24px;

  &:hover {
    background-color: #e9ecef;
    border-color: #0066cc;
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

// Компонент шага 1 - Выбор правовой формы
const LegalFormStep = ({ formData, updateFormData }) => {
  return (
    <StepContent>
      <StepTitle>Регистрация</StepTitle>

      <ProgressContainer>
        <ProgressText>Правовая форма</ProgressText>
      </ProgressContainer>

      <RadioGroup>
        <RadioOption
          selected={formData.legalForm === "individual"}
          onClick={() => updateFormData({ legalForm: "individual" })}
        >
          <RadioLabel>Я физическое лицо</RadioLabel>
          <RadioButton selected={formData.legalForm === "individual"} />
        </RadioOption>

        <RadioOption
          selected={formData.legalForm === "legal"}
          onClick={() => updateFormData({ legalForm: "legal" })}
        >
          <RadioLabel>Я юридическое лицо</RadioLabel>
          <RadioButton selected={formData.legalForm === "legal"} />
        </RadioOption>
      </RadioGroup>
    </StepContent>
  );
};

// Компонент шага 2 - Данные пользователя
const UserDataStep = ({ formData, updateFormData, onBack }) => {
  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Данные пользователя</StepTitle>

      <FormGroup>
        <Label>Имя*</Label>
        <Input
          type="text"
          placeholder="Введите имя"
          value={formData.firstName || ""}
          onChange={(e) => updateFormData({ firstName: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>Фамилия*</Label>
        <Input
          type="text"
          placeholder="Введите фамилию"
          value={formData.lastName || ""}
          onChange={(e) => updateFormData({ lastName: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>Отчество</Label>
        <Input
          type="text"
          placeholder="Введите отчество"
          value={formData.middleName || ""}
          onChange={(e) => updateFormData({ middleName: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>Мобильный телефон*</Label>
        <Input
          type="tel"
          placeholder="+7 (___) ___-__-__"
          value={formData.phone || ""}
          onChange={(e) => updateFormData({ phone: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>Электронная почта*</Label>
        <Input
          type="email"
          placeholder="email"
          value={formData.email || ""}
          onChange={(e) => updateFormData({ email: e.target.value })}
          disabled
        />
      </FormGroup>

      <FormGroup>
        <Label>Должность</Label>
        <Input
          type="text"
          placeholder="Введите свою должность"
          value={formData.position || ""}
          onChange={(e) => updateFormData({ position: e.target.value })}
        />
      </FormGroup>
    </StepContent>
  );
};

// Компонент шага 3 - Выбор города
const CityStep = ({ formData, updateFormData, onBack }) => {
  const cities = [
    { id: "01", name: "Нур-Султан" },
    { id: "02", name: "Алматы" },
    { id: "04", name: "Актюбинская область" },
    { id: "05", name: "Алматинская область" },
    { id: "06", name: "Атырауская область" },
    { id: "07", name: "Западно-Казахстанская область" },
    { id: "08", name: "Жамбылская область" },
    { id: "09", name: "Карагандинская область" },
    { id: "10", name: "Костанайская область" },
    { id: "11", name: "Кызылординская область" },
    { id: "12", name: "Мангистауская область" },
    { id: "13", name: "Туркестанская область" },
    { id: "14", name: "Павлодарская область" },
    { id: "15", name: "Северо-Казахстанская область" },
    { id: "16", name: "Восточно-Казахстанская область" },
    { id: "17", name: "Акмолинская область" },
    { id: "3 ", name: "Шымкент" },
  ];

  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Ваш город?</StepTitle>

      <FormGroup>
        <Label>Выберите из списка</Label>
        <RadioGroup>
          {cities.map((city) => (
            <RadioOption
              key={city.id}
              selected={formData.cityId === city.id}
              onClick={() =>
                updateFormData({
                  cityId: city.id,
                  city: city.name,
                })
              }
            >
              <RadioLabel>{city.name}</RadioLabel>
              <RadioButton selected={formData.cityId === city.id} />
            </RadioOption>
          ))}
        </RadioGroup>
      </FormGroup>
    </StepContent>
  );
};

// Компонент шага 4 - Загрузка изображения профиля
const ProfileImageStep = ({ formData, updateFormData, onBack }) => {
  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Добавьте изображение профиля</StepTitle>

      <ImageUploadContainer>
        <ImageUploadArea>
          <CameraIcon>📷</CameraIcon>
        </ImageUploadArea>
      </ImageUploadContainer>

      <SkipButton>Добавить позже</SkipButton>
    </StepContent>
  );
};

// Дополнительные стили для загрузки изображения
const ImageUploadContainer = styled.div`
  display: flex;
  justify-content: center;
  margin: 40px 0;
`;

const ImageUploadArea = styled.div`
  width: 120px;
  height: 120px;
  border: 2px dashed #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #0066cc;
    background-color: #f0f8ff;
  }
`;

const CameraIcon = styled.div`
  font-size: 32px;
  color: #0066cc;
`;

const SkipButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  font-size: 16px;
  cursor: pointer;
  text-decoration: underline;
  display: block;
  margin: 0 auto;

  &:hover {
    color: #0066cc;
  }
`;

// Компонент шага 5 - Данные компании (для юр.лица)
const CompanyDataStep = ({ formData, updateFormData, onBack }) => {
  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Данные компании</StepTitle>

      <CompanySection>
        <SectionTitle>Компания</SectionTitle>

        <FormGroup>
          <Label>Страна</Label>
          <Input type="text" value="Казахстан" disabled />
        </FormGroup>

        <FormGroup>
          <Label>Наименование компании*</Label>
          <Input
            type="text"
            placeholder="Введите наименование компании"
            value={formData.companyName || ""}
            onChange={(e) => updateFormData({ companyName: e.target.value })}
          />
        </FormGroup>

        <FormGroup>
          <Label>БИН</Label>
          <Input
            type="text"
            placeholder="Введите БИН"
            value={formData.bin || ""}
            onChange={(e) => updateFormData({ bin: e.target.value })}
          />
        </FormGroup>

        <FormGroup>
          <Label>Юридический адрес</Label>
          <Input
            type="text"
            placeholder="Введите юридический адрес"
            value={formData.legalAddress || ""}
            onChange={(e) => updateFormData({ legalAddress: e.target.value })}
          />
        </FormGroup>

        <CheckboxContainer>
          <Checkbox
            type="checkbox"
            id="sameAddress"
            checked={formData.sameAddress || false}
            onChange={(e) => updateFormData({ sameAddress: e.target.checked })}
          />
          <CheckboxLabel htmlFor="sameAddress">
            Адрес совпадает с фактическим
          </CheckboxLabel>
        </CheckboxContainer>

        <FormGroup>
          <Label>Фактический адрес</Label>
          <Input
            type="text"
            placeholder="Введите фактический адрес"
            value={formData.factualAddress || ""}
            onChange={(e) => updateFormData({ factualAddress: e.target.value })}
            disabled={formData.sameAddress}
          />
        </FormGroup>

        <FormGroup>
          <Label>Телефон контактного лица</Label>
          <Input
            type="tel"
            placeholder="+7 (___) ___-__-__"
            value={formData.contactPhone || ""}
            onChange={(e) => updateFormData({ contactPhone: e.target.value })}
          />
        </FormGroup>
      </CompanySection>
    </StepContent>
  );
};

const CompanyBankStep = ({ formData, updateFormData, onBack }) => {
  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Данные банка</StepTitle>

      <FormGroup>
        <Label>Наименование банка*</Label>
        <Input
          type="text"
          placeholder="Введите наименование банка"
          value={formData.bankName || ""}
          onChange={(e) => updateFormData({ bankName: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>КБЕ*</Label>
        <Input
          type="text"
          placeholder="Введите КБЕ (только цифры, макс. 2)"
          value={formData.kbe || ""}
          onChange={(e) => {
            const value = e.target.value.replace(/\D/g, ""); // Только цифры
            if (value.length <= 2) {
              updateFormData({ kbe: value });
            }
          }}
          maxLength="2"
          pattern="[0-9]*"
          inputMode="numeric"
        />
      </FormGroup>

      <FormGroup>
        <Label>ИИК*</Label>
        <Input
          type="text"
          placeholder="Введите ИИК"
          value={formData.iik || ""}
          onChange={(e) => updateFormData({ iik: e.target.value })}
        />
      </FormGroup>

      <FormGroup>
        <Label>БИК*</Label>
        <Input
          type="text"
          placeholder="Введите БИК"
          value={formData.bik || ""}
          onChange={(e) => updateFormData({ bik: e.target.value })}
        />
      </FormGroup>
    </StepContent>
  );
};

// Дополнительные стили для данных компании
const CompanySection = styled.div`
  margin-top: 24px;
`;

const SectionTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: #0066cc;
`;

const CheckboxLabel = styled.label`
  font-size: 14px;
  color: #333;
  cursor: pointer;
`;

// Основной компонент мастера регистрации
const RegistrationWizard = ({ initialEmail, onComplete, onCancel }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createUserProfile } = useAuth();
  const [formData, setFormData] = useState({
    email: initialEmail || "",
    legalForm: "",
    firstName: "",
    lastName: "",
    middleName: "",
    phone: "",
    bankName: "",
    kbe: "",
    iik: "",
    bik: "",
    city: "",
    cityId: "",
    profileImage: null,
    companyName: "",
    bin: "",
    legalAddress: "",
    factualAddress: "",
    sameAddress: false,
    contactPhone: "",
    activities: [],
    activitySearch: "",
    portfolio: [],
    certificates: [],
    companyStory: "",
  });

  const updateFormData = (newData) => {
    setFormData((prev) => ({ ...prev, ...newData }));
  };

  const nextStep = () => {
    if (currentStep < getTotalSteps()) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const getTotalSteps = () => {
    return formData.legalForm === "legal" ? 10 : 4; // Для юр.лица больше шагов
  };

  const getProgressPercentage = () => {
    return Math.round((currentStep / getTotalSteps()) * 100);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return formData.legalForm !== "";
      case 2:
        return (
          formData.firstName &&
          formData.lastName &&
          formData.phone &&
          formData.email
        );
      case 3:
        return formData.cityId !== "";
      case 4:
        return true; // Загрузка изображения необязательна
      case 5:
        return formData.legalForm === "individual" || formData.companyName;
      case 6:
        // Для CompanyBankStep проверяем банковские данные
        return (
          formData.legalForm === "individual" ||
          (formData.bankName && formData.kbe && formData.iik)
        );
      case 7:
        // Для ActivityTypesStep проверяем виды деятельности
        return (
          formData.legalForm === "individual" || formData.activities.length > 0
        );
      case 8:
        // Для PortfolioStep - необязательный шаг
        return true;
      case 9:
        // Для CertificatesStep - необязательный шаг
        return true;
      case 10:
        // Для CompanyStoryStep - необязательный шаг
        return true;

      default:
        return true;
    }
  };

  const handleComplete = async () => {
    setIsSubmitting(true);

    try {
      console.log("📝 Завершение регистрации с данными:", formData);

      // Отправляем данные профиля в API
      const result = await createUserProfile(formData);

      if (result.success) {
        console.log("✅ Профиль пользователя создан успешно");

        // Вызываем callback родительского компонента
        if (onComplete) {
          onComplete(formData);
        }
      } else {
        console.error("❌ Ошибка при создании профиля:", result.error);
        alert(`Ошибка при создании профиля: ${result.error}`);
      }
    } catch (error) {
      console.error("❌ Неожиданная ошибка при создании профиля:", error);
      alert("Произошла ошибка при создании профиля. Попробуйте еще раз.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <LegalFormStep formData={formData} updateFormData={updateFormData} />
        );
      case 2:
        return (
          <UserDataStep
            formData={formData}
            updateFormData={updateFormData}
            onBack={prevStep}
          />
        );
      case 3:
        return (
          <CityStep
            formData={formData}
            updateFormData={updateFormData}
            onBack={prevStep}
          />
        );
      case 4:
        return (
          <ProfileImageStep
            formData={formData}
            updateFormData={updateFormData}
            onBack={prevStep}
          />
        );
      case 5:
        if (formData.legalForm === "legal") {
          return (
            <CompanyDataStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;
      case 6:
        if (formData.legalForm === "legal") {
          return (
            <CompanyBankStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;
      case 7:
        if (formData.legalForm === "legal") {
          return (
            <ActivityTypesStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;
      case 8:
        if (formData.legalForm === "legal") {
          return (
            <PortfolioStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;
      case 9:
        if (formData.legalForm === "legal") {
          return (
            <CertificatesStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;
      case 10:
        if (formData.legalForm === "legal") {
          return (
            <CompanyStoryStep
              formData={formData}
              updateFormData={updateFormData}
              onBack={prevStep}
            />
          );
        }
        break;

      default:
        return (
          <LegalFormStep formData={formData} updateFormData={updateFormData} />
        );
    }
  };

  const isLastStep = currentStep === getTotalSteps();

  return (
    <WizardContainer>
      <WizardHeader>
        <ProgressContainer>
          <ProgressText>
            Подробная информация о компании, увеличивает шансы на заключение
            сделки именно с вами.
          </ProgressText>
          <ProgressBarContainer>
            <ProgressBar>
              <ProgressFill percentage={getProgressPercentage()} />
            </ProgressBar>
            <ProgressPercentage>{getProgressPercentage()}%</ProgressPercentage>
          </ProgressBarContainer>
        </ProgressContainer>
      </WizardHeader>

      {renderCurrentStep()}

      <ButtonContainer>
        <PrimaryButton
          onClick={isLastStep ? handleComplete : nextStep}
          disabled={!canProceed() || isSubmitting}
        >
          {isSubmitting
            ? "Создание профиля..."
            : isLastStep
            ? "Завершить регистрацию"
            : "Продолжить"}
        </PrimaryButton>
      </ButtonContainer>
    </WizardContainer>
  );
};

export default RegistrationWizard;
