"use client";

import { useEffect, useState } from "react";

// Компонент для генерации внутренних ссылок для SEO
// Помогает поисковикам находить товары, не включенные в sitemap
export default function SEOLinks({ currentPage, totalPages, categoryCode }) {
  const [seoLinks, setSeoLinks] = useState([]);

  useEffect(() => {
    // Генерируем ссылки на соседние страницы для лучшей индексации
    const links = [];
    
    // Ссылки на предыдущие/следующие страницы
    if (currentPage > 1) {
      links.push({
        href: `/products/page/${currentPage - 1}`,
        text: `Страница ${currentPage - 1}`,
        rel: "prev"
      });
    }
    
    if (currentPage < totalPages) {
      links.push({
        href: `/products/page/${currentPage + 1}`,
        text: `Страница ${currentPage + 1}`,
        rel: "next"
      });
    }

    // Ссылки на ключевые страницы (каждая 10-я)
    const keyPages = [];
    for (let i = 1; i <= Math.min(totalPages, 1000); i += 10) {
      if (i !== currentPage) {
        keyPages.push(i);
      }
    }
    
    keyPages.slice(0, 20).forEach(page => {
      links.push({
        href: categoryCode 
          ? `/products/category/${categoryCode}/page/${page}`
          : `/products/page/${page}`,
        text: `Страница ${page}`,
        rel: "related"
      });
    });

    setSeoLinks(links);
  }, [currentPage, totalPages, categoryCode]);

  return (
    <div style={{ display: "none" }}>
      {/* Скрытые ссылки для поисковых роботов */}
      <nav aria-label="SEO Navigation">
        {seoLinks.map((link, index) => (
          <a
            key={index}
            href={link.href}
            rel={link.rel}
            style={{ display: "none" }}
          >
            {link.text}
          </a>
        ))}
      </nav>
      
      {/* Дополнительные ссылки для глубокой индексации */}
      <div>
        <a href="/products/page/1" style={{ display: "none" }}>
          Первая страница каталога
        </a>
        <a href="/products/page/100" style={{ display: "none" }}>
          Страница 100
        </a>
        <a href="/products/page/500" style={{ display: "none" }}>
          Страница 500
        </a>
        <a href="/products/page/1000" style={{ display: "none" }}>
          Страница 1000
        </a>
      </div>
    </div>
  );
}
