import React from "react";
import styled from "styled-components";

const FiltersContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 16px;
  min-height: 32px; /* Резервируем минимальную высоту для предотвращения сдвига */
  align-items: flex-start;
`;

const FilterTag = styled.div`
  background-color: #f0f7ff;
  border: 1px solid #cce4ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: #0066cc;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: #0066cc;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  display: flex;
  align-items: center;
`;

const ActiveFilters = ({
  selectedCategory,
  searchQuery,
  onRemoveCategory,
  onRemoveSearch,
  isServerRendered = false,
  router = null,
}) => {
  const handleRemoveCategory = () => {
    if (isServerRendered && router) {
      // Для SSR страниц - переходим на страницу без категории
      if (searchQuery) {
        // Если есть поиск, переходим на страницу поиска
        const newUrl = `/products/page/1?name=${encodeURIComponent(
          searchQuery
        )}`;
        window.location.href = newUrl;
      } else {
        // Иначе на общую страницу товаров
        window.location.href = "/products/page/1";
      }
    } else {
      // Обычная клиентская логика
      onRemoveCategory && onRemoveCategory();
    }
  };

  const handleRemoveSearch = () => {
    if (isServerRendered && router) {
      // Для SSR страниц - переходим на страницу без поиска
      if (selectedCategory) {
        // Если есть категория, переходим на страницу категории
        const categoryCode = getCategoryCode(selectedCategory);
        if (categoryCode) {
          window.location.href = `/products/category/${categoryCode}/page/1`;
        } else {
          window.location.href = "/products/page/1";
        }
      } else {
        // Иначе на общую страницу товаров
        window.location.href = "/products/page/1";
      }
    } else {
      // Обычная клиентская логика
      onRemoveSearch && onRemoveSearch();
    }
  };

  // Получаем код категории для URL
  const getCategoryCode = (category) => {
    if (!category) return null;

    if (category.groupCode) return category.groupCode;
    if (category.subsectionCode) return category.subsectionCode;
    if (category.sectionCode) return category.sectionCode;
    if (category.departmentCode) return category.departmentCode;

    return null;
  };

  // Получаем название категории для отображения
  const getCategoryDisplayName = (category) => {
    if (!category) return "";

    if (category.groupName) return category.groupName;
    if (category.subsectionName) return category.subsectionName;
    if (category.sectionName) return category.sectionName;
    if (category.departmentName) return category.departmentName;

    return "Категория";
  };

  return (
    <FiltersContainer>
      {selectedCategory && (
        <FilterTag>
          {selectedCategory.groupName
            ? `Группа: ${selectedCategory.groupName}`
            : selectedCategory.subsectionName
            ? `Подраздел: ${selectedCategory.subsectionName}`
            : selectedCategory.sectionName
            ? `Раздел: ${selectedCategory.sectionName}`
            : `Отдел: ${selectedCategory.departmentName}`}
          <RemoveButton
            onClick={handleRemoveCategory}
            aria-label="Очистить фильтр категории"
            type="button"
          >
            ×
          </RemoveButton>
        </FilterTag>
      )}

      {searchQuery && (
        <FilterTag>
          Поиск: {searchQuery}
          <RemoveButton
            onClick={handleRemoveSearch}
            aria-label="Очистить поисковый запрос"
            type="button"
          >
            ×
          </RemoveButton>
        </FilterTag>
      )}
    </FiltersContainer>
  );
};

export default ActiveFilters;
