'use client';

import React from "react";
import styled from "styled-components";

const ProfileContainer = styled.div`
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 8px;
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e9ecef;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
`;

const ProfileImage = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #0066cc;
  border: 2px solid #e9ecef;
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.h2`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
`;

const ProfileEmail = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 4px;
`;

const ProfilePhone = styled.p`
  font-size: 16px;
  color: #666;
  margin: 0;
`;

const ProfileSection = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SectionContent = styled.div`
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;

  &:last-child {
    border-bottom: none;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
`;

const InfoLabel = styled.span`
  font-weight: 500;
  color: #666;
  font-size: 14px;
`;

const InfoValue = styled.span`
  color: #333;
  font-size: 14px;
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
`;

const Tag = styled.span`
  background-color: #0066cc;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
`;

const PortfolioItem = styled.div`
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`;

const PortfolioTitle = styled.h4`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
`;

const PortfolioDescription = styled.p`
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const Button = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
`;

const PrimaryButton = styled(Button)`
  background-color: #0066cc;
  color: white;

  &:hover {
    background-color: #0055b3;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: white;
  color: #666;
  border: 1px solid #e9ecef;

  &:hover {
    background-color: #f8f9fa;
  }
`;

const UserProfileDisplay = ({ profileData, onEdit, onLogout }) => {
  if (!profileData) {
    return (
      <ProfileContainer>
        <ProfileHeader>
          <ProfileImage>👤</ProfileImage>
          <ProfileInfo>
            <ProfileName>Профиль не заполнен</ProfileName>
            <ProfileEmail>Заполните профиль для лучшего взаимодействия</ProfileEmail>
          </ProfileInfo>
        </ProfileHeader>
        <ActionButtons>
          <PrimaryButton onClick={onEdit}>Заполнить профиль</PrimaryButton>
          <SecondaryButton onClick={onLogout}>Выйти</SecondaryButton>
        </ActionButtons>
      </ProfileContainer>
    );
  }

  return (
    <ProfileContainer>
      <ProfileHeader>
        <ProfileImage>
          {profileData.profileImage ? '🖼️' : '👤'}
        </ProfileImage>
        <ProfileInfo>
          <ProfileName>
            {profileData.firstName} {profileData.lastName}
          </ProfileName>
          <ProfileEmail>{profileData.email}</ProfileEmail>
          <ProfilePhone>{profileData.phone}</ProfilePhone>
        </ProfileInfo>
      </ProfileHeader>

      {/* Основная информация */}
      <ProfileSection>
        <SectionTitle>📋 Основная информация</SectionTitle>
        <SectionContent>
          <InfoRow>
            <InfoLabel>Правовая форма:</InfoLabel>
            <InfoValue>
              {profileData.legalForm === 'individual' ? 'Физическое лицо' : 'Юридическое лицо'}
            </InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Город:</InfoLabel>
            <InfoValue>{profileData.city}</InfoValue>
          </InfoRow>
          {profileData.middleName && (
            <InfoRow>
              <InfoLabel>Отчество:</InfoLabel>
              <InfoValue>{profileData.middleName}</InfoValue>
            </InfoRow>
          )}
        </SectionContent>
      </ProfileSection>

      {/* Информация о компании (для юр.лиц) */}
      {profileData.legalForm === 'legal' && profileData.companyName && (
        <ProfileSection>
          <SectionTitle>🏢 Информация о компании</SectionTitle>
          <SectionContent>
            <InfoRow>
              <InfoLabel>Название:</InfoLabel>
              <InfoValue>{profileData.companyName}</InfoValue>
            </InfoRow>
            {profileData.bin && (
              <InfoRow>
                <InfoLabel>БИН:</InfoLabel>
                <InfoValue>{profileData.bin}</InfoValue>
              </InfoRow>
            )}
            {profileData.legalAddress && (
              <InfoRow>
                <InfoLabel>Юридический адрес:</InfoLabel>
                <InfoValue>{profileData.legalAddress}</InfoValue>
              </InfoRow>
            )}
            {profileData.contactPhone && (
              <InfoRow>
                <InfoLabel>Контактный телефон:</InfoLabel>
                <InfoValue>{profileData.contactPhone}</InfoValue>
              </InfoRow>
            )}
          </SectionContent>
        </ProfileSection>
      )}

      {/* Виды деятельности */}
      {profileData.activities && profileData.activities.length > 0 && (
        <ProfileSection>
          <SectionTitle>🔧 Виды деятельности</SectionTitle>
          <SectionContent>
            <TagContainer>
              {profileData.activities.map((activity, index) => (
                <Tag key={index}>{activity}</Tag>
              ))}
            </TagContainer>
          </SectionContent>
        </ProfileSection>
      )}

      {/* Портфолио */}
      {profileData.portfolio && profileData.portfolio.length > 0 && (
        <ProfileSection>
          <SectionTitle>💼 Портфолио</SectionTitle>
          <SectionContent>
            {profileData.portfolio.map((item, index) => (
              <PortfolioItem key={index}>
                <PortfolioTitle>{item.name}</PortfolioTitle>
                <PortfolioDescription>{item.description}</PortfolioDescription>
              </PortfolioItem>
            ))}
          </SectionContent>
        </ProfileSection>
      )}

      {/* Сертификаты */}
      {profileData.certificates && profileData.certificates.length > 0 && (
        <ProfileSection>
          <SectionTitle>🏆 Сертификаты и лицензии</SectionTitle>
          <SectionContent>
            {profileData.certificates.map((item, index) => (
              <PortfolioItem key={index}>
                <PortfolioTitle>{item.name}</PortfolioTitle>
                <PortfolioDescription>{item.description}</PortfolioDescription>
              </PortfolioItem>
            ))}
          </SectionContent>
        </ProfileSection>
      )}

      {/* О компании */}
      {profileData.companyStory && (
        <ProfileSection>
          <SectionTitle>📖 О компании</SectionTitle>
          <SectionContent>
            <PortfolioDescription>{profileData.companyStory}</PortfolioDescription>
          </SectionContent>
        </ProfileSection>
      )}

      <ActionButtons>
        <PrimaryButton onClick={onEdit}>Редактировать профиль</PrimaryButton>
        <SecondaryButton onClick={onLogout}>Выйти из системы</SecondaryButton>
      </ActionButtons>
    </ProfileContainer>
  );
};

export default UserProfileDisplay;
