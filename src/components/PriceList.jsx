"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import SearchBarHierarchical from "./SearchBarHierarchical";
import ProductCard from "./ProductCard";
import ProductCardSkeleton from "./ProductCardSkeleton";
import Pagination from "./Pagination";
import ActiveFilters from "./ActiveFilters";

// Хуки больше не используются - заменены на SSR
import { useAveragePricesContext } from "../context/AveragePricesContext";

const PriceListContainer = styled.div`
  padding: 24px;
  flex-grow: 1;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;
PriceListContainer.displayName = "PriceListContainer";

const Title = styled.h1`
  font-size: 42px;
  line-height: 150%;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 22px;
    margin-bottom: 12px;
    display: none; /* Скрываем заголовок на мобильных, так как он уже есть в шапке */
  }
`;
Title.displayName = "Title";

const Description = styled.p`
  font-size: 17px;
  max-width: 656px;
  color: #666;
  margin-bottom: 24px;
  line-height: 150%;

  a {
    color: #0066cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  @media (max-width: 768px) {
    font-size: 13px;
    margin-bottom: 16px;
  }
`;
Description.displayName = "Description";

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 24px;
  transition: opacity 0.3s ease-in-out;

  /* Все карточки в строке будут одинаковой высоты */
  & > * {
    height: 100%;
    animation: fadeInUp 0.4s ease-out;
    animation-fill-mode: both;
  }

  /* Анимация появления карточек */
  & > *:nth-child(1) {
    animation-delay: 0.05s;
  }
  & > *:nth-child(2) {
    animation-delay: 0.1s;
  }
  & > *:nth-child(3) {
    animation-delay: 0.15s;
  }
  & > *:nth-child(4) {
    animation-delay: 0.2s;
  }
  & > *:nth-child(5) {
    animation-delay: 0.25s;
  }
  & > *:nth-child(6) {
    animation-delay: 0.3s;
  }
  & > *:nth-child(7) {
    animation-delay: 0.35s;
  }
  & > *:nth-child(8) {
    animation-delay: 0.4s;
  }
  & > *:nth-child(9) {
    animation-delay: 0.45s;
  }
  & > *:nth-child(10) {
    animation-delay: 0.5s;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }
`;
ProductGrid.displayName = "ProductGrid";

const FilterContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
`;
FilterContainer.displayName = "FilterContainer";

const FilterTag = styled.div`
  background-color: #f0f7ff;
  border: 1px solid #cce4ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  color: #0066cc;
  display: flex;
  align-items: center;
  gap: 8px;

  button {
    background: none;
    border: none;
    color: #0066cc;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    display: flex;
    align-items: center;
  }
`;
FilterTag.displayName = "FilterTag";

const InfoText = styled.p`
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 14px;
`;
InfoText.displayName = "InfoText";

// Ключи для хранения состояния фильтров в localStorage
const STORAGE_KEYS = {
  CATEGORY: "priceList.selectedCategory",
  SEARCH: "priceList.searchQuery",
  PAGE: "priceList.currentPage",
};

// Функция для сохранения состояния фильтров
const saveFiltersState = (category, query, page) => {
  try {
    if (typeof window !== "undefined") {
      localStorage.setItem(STORAGE_KEYS.CATEGORY, JSON.stringify(category));
      localStorage.setItem(STORAGE_KEYS.SEARCH, query);
      localStorage.setItem(STORAGE_KEYS.PAGE, page.toString());
    }
  } catch (error) {
    console.error("Ошибка при сохранении состояния фильтров:", error);
  }
};

// Функция для загрузки состояния фильтров
const loadFiltersState = () => {
  try {
    if (typeof window !== "undefined") {
      const category = localStorage.getItem(STORAGE_KEYS.CATEGORY);
      const query = localStorage.getItem(STORAGE_KEYS.SEARCH) || "";
      const page = parseInt(localStorage.getItem(STORAGE_KEYS.PAGE) || "1", 10);

      return {
        category: category ? JSON.parse(category) : null,
        query,
        page,
      };
    }
    return { category: null, query: "", page: 1 };
  } catch (error) {
    console.error("Ошибка при загрузке состояния фильтров:", error);
    return { category: null, query: "", page: 1 };
  }
};

const PriceList = ({
  initialData = null,
  isServerRendered = false,
  categoryInfo = null,
}) => {
  const router = useRouter();
  const { updatePricesForProducts } = useAveragePricesContext();

  // Загружаем сохраненное состояние фильтров при инициализации
  // const [initialState, setInitialState] = useState({
  //   category: null,
  //   query: "",
  //   page: 1,
  // });
  const [isClient, setIsClient] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Защита от ошибок гидратации
  if (typeof window === "undefined" && !isServerRendered) {
    return null;
  }

  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(
    initialData ? initialData.currentPage : 1
  );
  // const [isTransitioning, setIsTransitioning] = useState(false);
  const PAGE_SIZE = 20; // Размер страницы для пагинации

  // Состояние для SSR данных
  const [ssrData] = useState(initialData);

  // Определяем, находимся ли мы на SSR странице
  const isSSRPage = isServerRendered || !!initialData || !!categoryInfo;

  // Инициализация на клиенте
  useEffect(() => {
    setIsClient(true);

    console.log("PriceList: Инициализация компонента:", {
      isServerRendered,
      isSSRPage,
      categoryInfo,
      initialData: !!initialData,
    });

    // Если это серверный рендеринг, не загружаем состояние из localStorage
    if (isSSRPage) {
      // Устанавливаем состояние на основе переданных данных
      if (categoryInfo) {
        setSelectedCategory({
          department: categoryInfo.id,
          departmentName: categoryInfo.name,
          departmentCode: categoryInfo.code,
        });
      }
      if (initialData?.filters?.name) {
        setSearchQuery(initialData.filters.name);
      }
    } else {
      // Обычная инициализация из localStorage
      const savedState = loadFiltersState();
      // setInitialState(savedState);
      setSelectedCategory(savedState.category);
      setSearchQuery(savedState.query);
      setCurrentPage(savedState.page);
    }
  }, [isServerRendered, isSSRPage, categoryInfo, initialData]);

  // Отслеживаем завершение первоначальной загрузки
  useEffect(() => {
    if (!isClient) return;

    const timer = setTimeout(
      () => {
        setIsInitialLoad(false);
      },
      isSSRPage ? 50 : 200
    );

    return () => clearTimeout(timer);
  }, [isClient, isSSRPage]);

  // ЗАГЛУШКИ: Хуки заменены на SSR, компонент больше не используется
  const paginatedData = {
    data: [],
    pagination: { totalItems: 0, totalPages: 1 },
  };
  const isLoadingPaginated = false;
  const paginatedError = null;

  const paginatedFilteredData = {
    data: [],
    pagination: { totalItems: 0, totalPages: 1 },
  };
  const isLoadingFilteredPaginated = false;
  const filteredPaginatedError = null;

  const paginatedSearchData = {
    data: [],
    pagination: { totalItems: 0, totalPages: 1 },
  };
  const isLoadingSearchPaginated = false;
  const searchPaginatedError = null;

  const paginatedSearchWithCategoryData = {
    data: [],
    pagination: { totalItems: 0, totalPages: 1 },
  };
  const isLoadingSearchWithCategoryPaginated = false;
  const searchWithCategoryPaginatedError = null;

  // Определяем, какие данные отображать
  const productsToDisplay =
    isServerRendered && ssrData
      ? ssrData.products // Используем SSR данные
      : searchQuery && selectedCategory
      ? paginatedSearchWithCategoryData.data // Двойная фильтрация: поиск + категория
      : searchQuery
      ? paginatedSearchData.data // Только поиск
      : selectedCategory
      ? paginatedFilteredData.data // Только категория
      : paginatedData.data; // Все продукты

  // Определяем информацию о пагинации
  const paginationInfo =
    isServerRendered && ssrData
      ? {
          totalItems: ssrData.totalCount,
          totalPages: ssrData.totalPages,
          currentPage: ssrData.currentPage,
        } // Используем SSR пагинацию
      : searchQuery && selectedCategory
      ? paginatedSearchWithCategoryData.pagination // Двойная фильтрация: поиск + категория
      : searchQuery
      ? paginatedSearchData.pagination // Только поиск
      : selectedCategory
      ? paginatedFilteredData.pagination // Только категория
      : paginatedData.pagination; // Все продукты

  // Определяем состояние загрузки
  const isLoading =
    !isClient ||
    (!isSSRPage &&
      (isLoadingPaginated ||
        isLoadingFilteredPaginated ||
        isLoadingSearchPaginated ||
        isLoadingSearchWithCategoryPaginated));

  // Определяем состояние обновления данных
  // const isFetching =
  //   isFetchingPaginated ||
  //   isFetchingFilteredPaginated ||
  //   isFetchingSearchPaginated ||
  //   isFetchingSearchWithCategoryPaginated;

  // Определяем ошибки
  const error =
    paginatedError ||
    filteredPaginatedError ||
    searchPaginatedError ||
    searchWithCategoryPaginatedError;

  // Обновляем средние цены и фотографии при изменении списка продуктов
  useEffect(() => {
    if (productsToDisplay && productsToDisplay.length > 0) {
      console.log(
        "PriceList: Обновляем цены и фотографии для",
        productsToDisplay.length,
        "продуктов"
      );
      // Теперь updatePricesForProducts обновляет и цены, и фотографии
      updatePricesForProducts(productsToDisplay);
    }
  }, [productsToDisplay, updatePricesForProducts]);

  // Функция генерации URL для SEO-ссылок
  const getPageUrl = (page) => {
    if (typeof window === "undefined") return "#";

    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);

    if (categoryInfo) {
      return `/products/category/${
        categoryInfo.code
      }/page/${page}?${searchParams.toString()}`;
    } else {
      return `/products/page/${page}?${searchParams.toString()}`;
    }
  };

  // Обработчик изменения страницы для SSR (fallback для старых браузеров)
  const handlePageChange = (page) => {
    if (isSSRPage) {
      // Для SSR страниц используем навигацию через router (fallback)
      window.location.href = getPageUrl(page);
    } else {
      // Обычная клиентская пагинация
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: "smooth" });
      saveFiltersState(selectedCategory, searchQuery, page);
    }
  };

  return (
    <PriceListContainer as="section">
      <Title as="h1">Строительные материалы инструменты оборудование</Title>

      <Description>
        Здесь Вы можете находить ценовые предложения наших партнеров. Если Вы
        являетесь поставщиком, то{" "}
        <a href="#" aria-label="Добавить ценовое предложение">
          добавьте ценовое предложение
        </a>
        , это бесплатно.
      </Description>

      {/* Показываем компонент поиска только после инициализации на клиенте */}
      {isClient && (
        <SearchBarHierarchical
          initialSearchQuery={searchQuery || initialData?.filters?.name || ""}
          onCategorySelect={(category) => {
            console.log("PriceList: Выбрана категория:", category);

            // Если это SSR страница, переходим на правильный URL с CODE
            if (isSSRPage && category) {
              const searchParams = new URLSearchParams(window.location.search);

              // Определяем самый глубокий уровень категории для URL
              let categoryCode = null;
              if (category.groupCode) {
                categoryCode = category.groupCode;
              } else if (category.subsectionCode) {
                categoryCode = category.subsectionCode;
              } else if (category.sectionCode) {
                categoryCode = category.sectionCode;
              } else if (category.departmentCode) {
                categoryCode = category.departmentCode;
              }

              if (categoryCode) {
                const newUrl = `/products/category/${categoryCode}/page/1?${searchParams.toString()}`;
                console.log(
                  "PriceList: Переход на SSR страницу категории:",
                  newUrl
                );
                window.location.href = newUrl;
                return;
              }
            }

            // Обычная клиентская логика
            setSelectedCategory(category);
            setCurrentPage(1);
            saveFiltersState(category, searchQuery, 1);
          }}
          onSearch={(query) => {
            console.log("PriceList: Поиск запущен:", {
              query,
              isServerRendered,
              isSSRPage,
              categoryInfo,
            });

            // Если это SSR страница, переходим на правильный URL с поиском
            if (isSSRPage) {
              if (query && query.trim()) {
                // Есть поисковый запрос
                if (categoryInfo && categoryInfo.code) {
                  // Если есть категория, добавляем поиск к категории
                  const newUrl = `/products/category/${
                    categoryInfo.code
                  }/page/1?name=${encodeURIComponent(query.trim())}`;
                  console.log(
                    "PriceList: Переход на URL с поиском в категории:",
                    newUrl
                  );
                  window.location.href = newUrl;
                } else {
                  // Иначе переходим на общую страницу с поиском
                  const newUrl = `/products/page/1?name=${encodeURIComponent(
                    query.trim()
                  )}`;
                  console.log(
                    "PriceList: Переход на URL с общим поиском:",
                    newUrl
                  );
                  window.location.href = newUrl;
                }
              } else {
                // Пустой поисковый запрос - убираем поиск
                if (categoryInfo && categoryInfo.code) {
                  // Если есть категория, переходим на страницу категории без поиска
                  const newUrl = `/products/category/${categoryInfo.code}/page/1`;
                  console.log(
                    "PriceList: Переход на URL категории без поиска:",
                    newUrl
                  );
                  window.location.href = newUrl;
                } else {
                  // Иначе переходим на общую страницу без поиска
                  const newUrl = `/products/page/1`;
                  console.log(
                    "PriceList: Переход на общую страницу без поиска:",
                    newUrl
                  );
                  window.location.href = newUrl;
                }
              }
              return;
            }

            // Обычная клиентская логика
            setSearchQuery(query);
            setCurrentPage(1);
            saveFiltersState(selectedCategory, query, 1);
          }}
        />
      )}

      {/* Показываем активные фильтры */}
      {(isClient || isServerRendered) && (
        <ActiveFilters
          selectedCategory={
            selectedCategory ||
            (categoryInfo
              ? {
                  departmentName: categoryInfo.name,
                  departmentCode: categoryInfo.code,
                }
              : null)
          }
          searchQuery={searchQuery || initialData?.filters?.name}
          onRemoveCategory={() => {
            setSelectedCategory(null);
            setCurrentPage(1);
            saveFiltersState(null, searchQuery, 1);
          }}
          onRemoveSearch={() => {
            setSearchQuery("");
            setCurrentPage(1);
            saveFiltersState(selectedCategory, "", 1);
          }}
          isServerRendered={isServerRendered}
          router={router}
        />
      )}

      {/* Показываем контент в зависимости от состояния */}
      {(() => {
        // Показываем ошибку в первую очередь
        if (error) {
          return (
            <div
              role="alert"
              style={{ textAlign: "center", padding: "20px", color: "red" }}
            >
              Ошибка: {error.message || "Произошла ошибка"}. Пожалуйста,
              обновите страницу или попробуйте позже.
            </div>
          );
        }

        // Показываем скелетоны во время первоначальной загрузки
        if (isInitialLoad || isLoading) {
          return (
            <ProductGrid as="ul" aria-label="Загрузка товаров">
              {Array.from({ length: PAGE_SIZE }).map((_, index) => (
                <li
                  key={`skeleton-${index}`}
                  style={{
                    listStyle: "none",
                    animationDelay: `${index * 0.05}s`,
                  }}
                >
                  <ProductCardSkeleton />
                </li>
              ))}
            </ProductGrid>
          );
        }

        // Показываем данные
        const hasData = isSSRPage
          ? ssrData && ssrData.products && ssrData.products.length > 0
          : isClient && productsToDisplay.length > 0;

        if (!hasData) {
          return (
            <div
              role="status"
              aria-live="polite"
              style={{
                marginTop: "24px",
                textAlign: "center",
                color: "#666",
              }}
            >
              По выбранным критериям товары не найдены
            </div>
          );
        }

        return (
          <>
            <ProductGrid
              as="ul"
              aria-label="Список товаров"
              style={{
                opacity: isInitialLoad ? 0 : 1,
                transform: isInitialLoad ? "translateY(20px)" : "translateY(0)",
                transition: "opacity 0.4s ease-out, transform 0.4s ease-out",
              }}
            >
              {productsToDisplay.map((product, index) => (
                <li
                  key={product.MaterialId}
                  style={{
                    listStyle: "none",
                    opacity: isInitialLoad ? 0 : 1,
                    transform: isInitialLoad
                      ? "translateY(10px)"
                      : "translateY(0)",
                    transition: `opacity 0.3s ease-out ${
                      index * 0.03
                    }s, transform 0.3s ease-out ${index * 0.03}s`,
                  }}
                >
                  <ProductCard product={product} />
                </li>
              ))}
            </ProductGrid>

            {/* Компонент пагинации */}
            {paginationInfo.totalPages > 1 && (
              <Pagination
                currentPage={paginationInfo.currentPage || currentPage}
                totalPages={paginationInfo.totalPages}
                totalItems={paginationInfo.totalItems}
                pageSize={PAGE_SIZE}
                onPageChange={handlePageChange}
                getPageUrl={getPageUrl}
                useSEOLinks={isSSRPage}
              />
            )}
          </>
        );
      })()}
    </PriceListContainer>
  );
};

PriceList.displayName = "PriceList";

export default PriceList;
