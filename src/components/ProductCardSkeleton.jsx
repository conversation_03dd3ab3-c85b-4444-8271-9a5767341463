"use client";

import React from "react";
import styled, { keyframes } from "styled-components";

// Анимация пульсации для скелетона
const pulse = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
`;

const Card = styled.div`
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.3s ease;
  height: 100%;
  opacity: 1;
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
Card.displayName = "Card";

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #e0e0e0;
  animation: ${pulse} 1.5s infinite ease-in-out;
`;
ImageContainer.displayName = "ImageContainer";

const ContentContainer = styled.div`
  padding: 16px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
`;
ContentContainer.displayName = "ContentContainer";

const TitleSkeleton = styled.div`
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-bottom: 12px;
  animation: ${pulse} 1.5s infinite ease-in-out;
`;
TitleSkeleton.displayName = "TitleSkeleton";

const PriceSkeleton = styled.div`
  height: 16px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-bottom: 8px;
  animation: ${pulse} 1.5s infinite ease-in-out;
  width: ${(props) => props.width || "100%"};
`;
PriceSkeleton.displayName = "PriceSkeleton";

const ButtonsSkeleton = styled.div`
  display: flex;
  gap: 8px;
  margin-top: auto;
  padding-top: 16px;
`;
ButtonsSkeleton.displayName = "ButtonsSkeleton";

const ButtonSkeleton = styled.div`
  height: 36px;
  background-color: #e0e0e0;
  border-radius: 8px;
  flex: 1;
  animation: ${pulse} 1.5s infinite ease-in-out;
`;
ButtonSkeleton.displayName = "ButtonSkeleton";

/**
 * Компонент скелетона для карточки товара
 * Отображается во время загрузки данных
 */
const ProductCardSkeleton = () => {
  return (
    <Card aria-busy="true" aria-label="Загрузка карточки товара">
      <ImageContainer />
      <ContentContainer>
        <TitleSkeleton />
        <div style={{ marginTop: "24px" }}>
          <PriceSkeleton width="80%" />
          <PriceSkeleton width="70%" />
          <PriceSkeleton width="90%" />
        </div>
        <ButtonsSkeleton>
          <ButtonSkeleton />
          <ButtonSkeleton />
        </ButtonsSkeleton>
      </ContentContainer>
    </Card>
  );
};

ProductCardSkeleton.displayName = "ProductCardSkeleton";

export default ProductCardSkeleton;
