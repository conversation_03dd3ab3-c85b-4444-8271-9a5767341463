"use client";

import React from "react";
import styled from "styled-components";

// Компонент шага 6 - Виды деятельности
const ActivityTypesStep = ({ formData, updateFormData, onBack }) => {
  const activities = [
    { id: 1, name: "Строительная компания", type: "Company" },
    { id: 2, name: "Грузоперевозка", type: "Carrier" },
    { id: 3, name: "Поставщик строительных материалов", type: "Provider" },
    { id: 4, name: "Проектировщик", type: "Company" },
    { id: 5, name: "Сметчик", type: "Company" },
  ];

  const toggleActivity = (activity) => {
    const currentActivities = formData.activities || [];
    const isSelected = currentActivities.some((a) => a.id === activity.id);

    const updatedActivities = isSelected
      ? currentActivities.filter((a) => a.id !== activity.id)
      : [...currentActivities, activity];

    updateFormData({ activities: updatedActivities });
  };

  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Виды деятельности</StepTitle>

      <SearchContainer>
        <SearchInput
          placeholder="Поиск по роду деятельности"
          value={formData.activitySearch || ""}
          onChange={(e) => updateFormData({ activitySearch: e.target.value })}
        />
      </SearchContainer>

      <ActivityList>
        {activities.map((activity) => (
          <ActivityOption
            key={activity.id}
            selected={formData.activities?.some((a) => a.id === activity.id)}
            onClick={() => toggleActivity(activity)}
          >
            <ActivityLabel>{activity.name}</ActivityLabel>
            <ActivityCheckbox
              selected={formData.activities?.some((a) => a.id === activity.id)}
            />
          </ActivityOption>
        ))}
      </ActivityList>
    </StepContent>
  );
};

// Компонент шага 7 - Портфолио
const PortfolioStep = ({ formData, updateFormData, onBack }) => {
  const addPortfolioItem = () => {
    const currentPortfolio = formData.portfolio || [];
    updateFormData({
      portfolio: [
        ...currentPortfolio,
        { name: "", description: "", images: [] },
      ],
    });
  };

  const updatePortfolioItem = (index, field, value) => {
    const currentPortfolio = formData.portfolio || [];
    const updatedPortfolio = [...currentPortfolio];
    updatedPortfolio[index] = { ...updatedPortfolio[index], [field]: value };
    updateFormData({ portfolio: updatedPortfolio });
  };

  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Портфолио</StepTitle>

      <SectionTitle>Портфолио</SectionTitle>

      {(formData.portfolio || []).map((item, index) => (
        <PortfolioItem key={index}>
          <FormGroup>
            <Label>Название работы*</Label>
            <Input
              type="text"
              placeholder="Например: отделка помещения"
              value={item.name || ""}
              onChange={(e) =>
                updatePortfolioItem(index, "name", e.target.value)
              }
            />
          </FormGroup>

          <FormGroup>
            <Label>Описание*</Label>
            <TextArea
              placeholder="Опишите какие работы были проделаны, их особенность, когда они были выполнены, в каких целях."
              value={item.description || ""}
              onChange={(e) =>
                updatePortfolioItem(index, "description", e.target.value)
              }
            />
          </FormGroup>

          <ImageUploadArea>
            <UploadIcon>📁</UploadIcon>
            <UploadText>
              Перетащите ваши картинки сюда или нажмите, чтобы выбрать и
              загрузить файлы обычным способом.
            </UploadText>
          </ImageUploadArea>
        </PortfolioItem>
      ))}

      <AddMoreButton onClick={addPortfolioItem}>Добавить еще +</AddMoreButton>

      <SkipStepButton>Пропустить этот шаг</SkipStepButton>
    </StepContent>
  );
};

// Компонент шага 8 - Сертификаты и лицензии
const CertificatesStep = ({ formData, updateFormData, onBack }) => {
  const addCertificate = () => {
    const currentCertificates = formData.certificates || [];
    updateFormData({
      certificates: [
        ...currentCertificates,
        { name: "", description: "", images: [] },
      ],
    });
  };

  const updateCertificate = (index, field, value) => {
    const currentCertificates = formData.certificates || [];
    const updatedCertificates = [...currentCertificates];
    updatedCertificates[index] = {
      ...updatedCertificates[index],
      [field]: value,
    };
    updateFormData({ certificates: updatedCertificates });
  };

  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Сертификаты и лицензии</StepTitle>

      <SectionTitle>Сертификаты, лицензии</SectionTitle>

      {(formData.certificates || []).map((item, index) => (
        <CertificateItem key={index}>
          <FormGroup>
            <Label>Название документа*</Label>
            <Input
              type="text"
              placeholder="Например: Свидетельство"
              value={item.name || ""}
              onChange={(e) => updateCertificate(index, "name", e.target.value)}
            />
          </FormGroup>

          <FormGroup>
            <Label>Описание*</Label>
            <TextArea
              placeholder="Расскажите, каким образом получили данный документ и какие преимущества он вам дает."
              value={item.description || ""}
              onChange={(e) =>
                updateCertificate(index, "description", e.target.value)
              }
            />
          </FormGroup>

          <ImageUploadArea>
            <UploadIcon>📁</UploadIcon>
            <UploadText>
              Перетащите ваши картинки сюда или нажмите, чтобы выбрать и
              загрузить файлы обычным способом.
            </UploadText>
          </ImageUploadArea>
        </CertificateItem>
      ))}

      <AddMoreButton onClick={addCertificate}>Добавить еще +</AddMoreButton>

      <SkipStepButton>Пропустить этот шаг</SkipStepButton>
    </StepContent>
  );
};

// Компонент шага 9 - Рассказ о компании
const CompanyStoryStep = ({ formData, updateFormData, onBack }) => {
  return (
    <StepContent>
      <BackButton onClick={onBack}>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
        </svg>
        Назад
      </BackButton>
      <StepTitle>Расскажите о компании</StepTitle>

      <SectionTitle>Расскажите о нас</SectionTitle>

      <FormGroup>
        <TextArea
          placeholder="Расскажите, о вашей компании, сколько лет присутствуете на рынке, какое количество работ выполнили и так далее."
          value={formData.companyStory || ""}
          onChange={(e) => updateFormData({ companyStory: e.target.value })}
          rows={8}
        />
      </FormGroup>

      <SkipStepButton>Пропустить этот шаг</SkipStepButton>
    </StepContent>
  );
};

// Styled Components
const StepContent = styled.div`
  margin-bottom: 32px;
`;

const StepTitle = styled.h3`
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
`;

const ProgressContainer = styled.div`
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
`;

const ProgressText = styled.p`
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
`;

const ProgressBarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background-color: #28a745;
  width: ${(props) => props.percentage}%;
  transition: width 0.3s ease;
`;

const ProgressPercentage = styled.span`
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
`;

const SectionTitle = styled.h4`
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
`;

const Input = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  transition: all 0.2s ease;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  }
`;

// Дополнительные стили
const SearchContainer = styled.div`
  margin-bottom: 24px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
  }
`;

const ActivityList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
`;

const ActivityOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${(props) => (props.selected ? "#f0f8ff" : "white")};
  border-color: ${(props) => (props.selected ? "#0066cc" : "#e9ecef")};

  &:hover {
    border-color: #0066cc;
  }
`;

const ActivityLabel = styled.span`
  font-size: 16px;
  color: #333;
`;

const ActivityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.selected ? "#0066cc" : "#e9ecef")};
  border-radius: 4px;
  background-color: ${(props) => (props.selected ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
    opacity: ${(props) => (props.selected ? 1 : 0)};
  }
`;

const PortfolioItem = styled.div`
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  background-color: #fafafa;
`;

const CertificateItem = styled.div`
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  background-color: #fafafa;
`;

const ImageUploadArea = styled.div`
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #0066cc;
    background-color: #f0f8ff;
  }
`;

const UploadIcon = styled.div`
  font-size: 32px;
  margin-bottom: 12px;
  color: #0066cc;
`;

const UploadText = styled.p`
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
`;

const AddMoreButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: white;
  border: 1px solid #0066cc;
  border-radius: 8px;
  color: #0066cc;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;

  &:hover {
    background-color: #f0f8ff;
  }
`;

const SkipStepButton = styled.button`
  background: none;
  border: none;
  color: #0066cc;
  font-size: 16px;
  cursor: pointer;
  text-decoration: underline;
  display: block;
  margin: 0 auto;

  &:hover {
    color: #0055b3;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #f8f9fa;
  color: #0066cc;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 24px;

  &:hover {
    background-color: #e9ecef;
    border-color: #0066cc;
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

export { ActivityTypesStep, PortfolioStep, CertificatesStep, CompanyStoryStep };
