"use client";

import styled from "styled-components";

const BrandBar = styled.div`
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 8px 0;
  font-size: 14px;
  color: #6c757d;
  text-align: center;
`;

const BrandLink = styled.a`
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    color: #0052a3;
    text-decoration: underline;
  }
`;

const ShopBadge = styled.span`
  background: #0066cc;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
`;

// Компонент для связи с основным сайтом SADI
export default function BrandConnection() {
  return (
    <>
      {/* Связь с основным сайтом */}
      <BrandBar>
        <div
          style={{ maxWidth: "1200px", margin: "0 auto", padding: "0 20px" }}
        >
          Часть экосистемы{" "}
          <BrandLink
            href="https://sadi.kz"
            target="_blank"
            rel="noopener noreferrer"
          >
            SADI.KZ
          </BrandLink>
          <ShopBadge>SHOP</ShopBadge> - интернет-магазин строительных материалов
        </div>
      </BrandBar>

      {/* Скрытые ссылки для SEO */}
      <div style={{ display: "none" }}>
        <a href="https://sadi.kz" rel="canonical">
          SADI.KZ - Главный портал строительных материалов
        </a>
        <a href="https://shop.sadi.kz">
          SADI Shop - Интернет-магазин строительных материалов
        </a>
        <link rel="alternate" href="https://sadi.kz" hrefLang="ru" />
      </div>
    </>
  );
}
