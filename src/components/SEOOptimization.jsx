// Компонент для дополнительных SEO оптимизаций

import { useEffect } from "react";
import API_CONFIG from "../config/api";

// Компонент для добавления дополнительных мета-тегов
export const AdditionalSEOTags = () => {
  return (
    <>
      {/* Дополнительные мета-теги для поисковых систем */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="msapplication-TileColor" content="#0066cc" />
      <meta name="theme-color" content="#0066cc" />

      {/* Open Graph дополнительные теги */}
      <meta property="og:site_name" content="SADI Shop" />
      <meta property="og:locale" content="ru_RU" />
      <meta property="og:locale:alternate" content="kk_KZ" />

      {/* Twitter Card дополнительные теги */}
      <meta name="twitter:site" content="@sadishop" />
      <meta name="twitter:creator" content="@sadishop" />

      {/* Дополнительные теги для поисковых систем */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="SADI Shop" />

      {/* Preconnect для ускорения загрузки */}
      <link rel="preconnect" href={API_CONFIG.BASE_URL} />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://mc.yandex.ru" />

      {/* DNS prefetch для внешних ресурсов */}
      <link
        rel="dns-prefetch"
        href={`//${API_CONFIG.BASE_URL.replace(/^https?:\/\//, "")}`}
      />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//mc.yandex.ru" />
    </>
  );
};

// Компонент для добавления hreflang тегов
export const HreflangTags = ({ currentUrl }) => {
  return (
    <>
      <link rel="alternate" hrefLang="ru" href={currentUrl} />
      <link rel="alternate" hrefLang="kk" href={currentUrl} />
      <link rel="alternate" hrefLang="x-default" href={currentUrl} />
    </>
  );
};

// Компонент для критических CSS стилей
export const CriticalCSS = () => {
  return (
    <style jsx>{`
      /* Критические стили для первого экрана */
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }

      .header {
        background-color: #0066cc;
        color: white;
        padding: 1rem;
      }

      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        font-size: 1.2rem;
        color: #666;
      }

      /* Оптимизация для Core Web Vitals */
      img {
        max-width: 100%;
        height: auto;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }
    `}</style>
  );
};

// Компонент для отслеживания производительности
export const PerformanceMonitoring = () => {
  useEffect(() => {
    // Отслеживание Core Web Vitals
    if (typeof window !== "undefined" && "PerformanceObserver" in window) {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];

        if (window.gtag) {
          window.gtag("event", "LCP", {
            event_category: "Web Vitals",
            value: Math.round(lastEntry.startTime),
            non_interaction: true,
          });
        }
      });

      try {
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
      } catch (e) {
        console.log("LCP observer not supported");
      }

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (window.gtag) {
            window.gtag("event", "FID", {
              event_category: "Web Vitals",
              value: Math.round(entry.processingStart - entry.startTime),
              non_interaction: true,
            });
          }
        });
      });

      try {
        fidObserver.observe({ entryTypes: ["first-input"] });
      } catch (e) {
        console.log("FID observer not supported");
      }

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });

        if (window.gtag) {
          window.gtag("event", "CLS", {
            event_category: "Web Vitals",
            value: Math.round(clsValue * 1000),
            non_interaction: true,
          });
        }
      });

      try {
        clsObserver.observe({ entryTypes: ["layout-shift"] });
      } catch (e) {
        console.log("CLS observer not supported");
      }
    }
  }, []);

  return null;
};

// Компонент для lazy loading изображений
export const LazyImage = ({ src, alt, className, ...props }) => {
  useEffect(() => {
    // Intersection Observer для lazy loading
    if ("IntersectionObserver" in window) {
      const images = document.querySelectorAll("img[data-src]");

      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.removeAttribute("data-src");
            imageObserver.unobserve(img);
          }
        });
      });

      images.forEach((img) => imageObserver.observe(img));
    }
  }, []);

  return (
    <img
      data-src={src}
      alt={alt}
      className={className}
      loading="lazy"
      decoding="async"
      {...props}
    />
  );
};

// Компонент для предзагрузки критических ресурсов
export const ResourcePreloading = () => {
  return (
    <>
      {/* Предзагрузка критических ресурсов */}
      <link
        rel="preload"
        href="/fonts/main.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />

      {/* Предзагрузка критических API */}
      <link
        rel="preload"
        href={`${API_CONFIG.BASE_URL}/api/Materials`}
        as="fetch"
        crossOrigin="anonymous"
      />

      {/* Предзагрузка изображений */}
      <link rel="preload" href="/images/placeholder.png" as="image" />
    </>
  );
};

export default {
  AdditionalSEOTags,
  HreflangTags,
  CriticalCSS,
  PerformanceMonitoring,
  LazyImage,
  ResourcePreloading,
};
