'use client';

import React from "react";
import styled from "styled-components";

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px;
`;
ErrorContainer.displayName = "ErrorContainer";

const ErrorIcon = styled.div`
  font-size: 64px;
  color: #dc3545;
  margin-bottom: 20px;
`;
ErrorIcon.displayName = "ErrorIcon";

const ErrorTitle = styled.h2`
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
`;
ErrorTitle.displayName = "ErrorTitle";

const ErrorMessage = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
  max-width: 500px;
  line-height: 1.5;
`;
ErrorMessage.displayName = "ErrorMessage";

const ErrorDetails = styled.details`
  margin-top: 20px;
  padding: 16px;
  background-color: #f1f3f4;
  border-radius: 4px;
  max-width: 600px;
  width: 100%;

  summary {
    cursor: pointer;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
  }

  pre {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    color: #333;
    border: 1px solid #e0e0e0;
  }
`;
ErrorDetails.displayName = "ErrorDetails";

const RetryButton = styled.button`
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-right: 12px;

  &:hover {
    background-color: #0055b3;
  }
`;
RetryButton.displayName = "RetryButton";

const HomeButton = styled.button`
  background-color: white;
  color: #333;
  border: 2px solid #d6dce1;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
    border-color: #0066cc;
  }
`;
HomeButton.displayName = "HomeButton";

const ButtonContainer = styled.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
`;
ButtonContainer.displayName = "ButtonContainer";

/**
 * Компонент для отображения ошибок
 * @param {Object} props - Свойства компонента
 * @param {Error} props.error - Объект ошибки
 * @param {Function} props.onRetry - Функция для повторной попытки
 * @param {Function} props.onGoHome - Функция для перехода на главную
 * @param {string} props.title - Заголовок ошибки
 * @param {string} props.message - Сообщение об ошибке
 * @returns {JSX.Element} - Компонент ошибки
 */
const ErrorBoundary = ({
  error,
  onRetry,
  onGoHome,
  title = "Что-то пошло не так",
  message = "Произошла неожиданная ошибка. Пожалуйста, попробуйте еще раз или обратитесь в службу поддержки.",
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Перезагружаем страницу по умолчанию
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome();
    } else {
      // Переходим на главную страницу по умолчанию
      window.location.href = "/";
    }
  };

  return (
    <ErrorContainer role="alert">
      <ErrorIcon>⚠️</ErrorIcon>
      <ErrorTitle>{title}</ErrorTitle>
      <ErrorMessage>{message}</ErrorMessage>

      <ButtonContainer>
        <RetryButton onClick={handleRetry}>Попробовать еще раз</RetryButton>
        <HomeButton onClick={handleGoHome}>На главную</HomeButton>
      </ButtonContainer>

      {error && (
        <ErrorDetails>
          <summary>Техническая информация</summary>
          <pre>
            {error.name}: {error.message}
            {error.stack && `\n\nStack trace:\n${error.stack}`}
          </pre>
        </ErrorDetails>
      )}
    </ErrorContainer>
  );
};

ErrorBoundary.displayName = "ErrorBoundary";

export default ErrorBoundary;
