"use client";

import React, { createContext, useState, useContext, useEffect } from "react";
import API_CONFIG, { AuthDebug } from "../config/api.js";
import authService from "../services/auth.service.js";

// Функция для перевода ошибок API на русский язык
const translateApiError = (errorMessage) => {
  const errorTranslations = {
    // Ошибки паролей
    "Passwords must have at least one lowercase ('a'-'z').":
      "Пароль должен содержать хотя бы одну строчную букву.",
    "Passwords must have at least one uppercase ('A'-'Z').":
      "Пароль должен содержать хотя бы одну заглавную букву.",
    "Passwords must have at least one digit ('0'-'9').":
      "Пароль должен содержать хотя бы одну цифру.",
    "Passwords must have at least one non alphanumeric character.":
      "Пароль должен содержать хотя бы один специальный символ.",
    "Passwords must be at least 6 characters.":
      "Пароль должен содержать минимум 6 символов.",
    "Passwords must be at least 8 characters.":
      "Пароль должен содержать минимум 8 символов.",

    // Ошибки email
    "is invalid.": "Некорректный email.",
    "Email is invalid.": "Некорректный email.",
    "is already taken.": "уже зарегистрирован.",
    "Email is already taken.": "Email уже зарегистрирован.",

    // Общие ошибки
    "The request is invalid.": "Некорректный запрос.",
    "User name is required.": "Имя пользователя обязательно.",
    "Email is required.": "Email обязателен.",
    "Password is required.": "Пароль обязателен.",
    "ConfirmPassword is required.": "Подтверждение пароля обязательно.",
    "The password and confirmation password do not match.":
      "Пароли не совпадают.",

    // Ошибки авторизации
    "Invalid username or password.": "Неверный email или пароль.",
    "Invalid grant": "Неверный email или пароль.",
    invalid_grant: "Неверный email или пароль.",
    "User not found.": "Пользователь не найден.",
    "Account is locked.": "Аккаунт заблокирован.",
    "Email not confirmed.": "Email не подтвержден.",
  };

  // Проверяем точные совпадения
  if (errorTranslations[errorMessage]) {
    return errorTranslations[errorMessage];
  }

  // Проверяем частичные совпадения для email ошибок
  if (errorMessage.includes("is invalid")) {
    return "Некорректный email.";
  }

  if (errorMessage.includes("is already taken")) {
    return "Пользователь с таким email уже зарегистрирован.";
  }

  // Проверяем ошибки паролей
  if (errorMessage.includes("Passwords must have at least one lowercase")) {
    return "Пароль должен содержать хотя бы одну строчную букву.";
  }

  if (errorMessage.includes("Passwords must have at least one uppercase")) {
    return "Пароль должен содержать хотя бы одну заглавную букву.";
  }

  if (errorMessage.includes("Passwords must have at least one digit")) {
    return "Пароль должен содержать хотя бы одну цифру.";
  }

  if (
    errorMessage.includes("Passwords must have at least one non alphanumeric")
  ) {
    return "Пароль должен содержать хотя бы один специальный символ.";
  }

  if (errorMessage.includes("Passwords must be at least")) {
    const match = errorMessage.match(/at least (\d+) characters/);
    const minLength = match ? match[1] : "6";
    return `Пароль должен содержать минимум ${minLength} символов.`;
  }

  // Если перевод не найден, возвращаем оригинальное сообщение
  return errorMessage;
};

// Создаем контекст для авторизации
const AuthContext = createContext();

// Хук для использования контекста авторизации
export const useAuth = () => {
  return useContext(AuthContext);
};

// Провайдер контекста авторизации
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Проверяем сохраненный токен при инициализации
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Сначала проверяем localStorage (для "Запомнить меня")
      let savedToken = localStorage.getItem("api_access_token");
      let savedUser = localStorage.getItem("user_data");
      let storageType = "localStorage";

      // Если в localStorage нет, проверяем sessionStorage
      if (!savedToken) {
        savedToken = sessionStorage.getItem("api_access_token");
        savedUser = sessionStorage.getItem("user_data");
        storageType = "sessionStorage";
      }

      AuthDebug.logTokenLoaded(savedToken);

      if (savedToken && savedUser) {
        try {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          setIsAuthenticated(true);
          authService.setToken(savedToken);
          console.log(`✅ Восстановлена авторизация из ${storageType}`);

          // Обновляем информацию о пользователе при восстановлении сессии
          fetchUserInfo(savedToken)
            .then((userInfo) => {
              if (userInfo) {
                const updatedUserData = {
                  ...userData,
                  userId: userInfo.UserId || userData.userId,
                  hasRegistered:
                    userInfo.HasRegistered || userData.hasRegistered,
                  loginProvider:
                    userInfo.LoginProvider || userData.loginProvider,
                };
                setUser(updatedUserData);

                // Обновляем сохраненные данные
                const storage =
                  storageType === "localStorage"
                    ? localStorage
                    : sessionStorage;
                storage.setItem("user_data", JSON.stringify(updatedUserData));
              }
            })
            .catch((error) => {
              console.warn(
                "⚠️ Не удалось обновить информацию о пользователе при восстановлении:",
                error
              );
            });
        } catch (error) {
          AuthDebug.logAuthError(
            error,
            "useEffect - восстановление авторизации"
          );
          // Очищаем оба хранилища при ошибке
          localStorage.removeItem("api_access_token");
          localStorage.removeItem("user_data");
          sessionStorage.removeItem("api_access_token");
          sessionStorage.removeItem("user_data");
          AuthDebug.logTokenCleared();
        }
      }
    }
  }, []);

  // Функция для получения информации о пользователе из /api/PUsers (для существующих пользователей)
  const fetchUserInfo = async (email) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/PUsers?email=${encodeURIComponent(email)}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const userInfo = await response.json();
        console.log("✅ Информация о пользователе получена:", userInfo);
        return userInfo;
      } else {
        console.warn(
          "⚠️ Не удалось получить информацию о пользователе:",
          response.status
        );
        return null;
      }
    } catch (error) {
      console.error(
        "❌ Ошибка при получении информации о пользователе:",
        error
      );
      return null;
    }
  };

  // Функция для получения базовой информации о пользователе из /api/Account/UserInfo (для новых пользователей)
  const fetchAccountUserInfo = async (token) => {
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/Account/UserInfo`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const userInfo = await response.json();
        console.log("✅ Базовая информация о пользователе получена:", userInfo);
        return userInfo;
      } else {
        console.warn(
          "⚠️ Не удалось получить базовую информацию о пользователе:",
          response.status
        );
        return null;
      }
    } catch (error) {
      console.error(
        "❌ Ошибка при получении базовой информации о пользователе:",
        error
      );
      return null;
    }
  };

  // Функция входа в систему
  const login = async (email, password, rememberMe = false) => {
    setIsLoading(true);
    setAuthError("");

    try {
      // Обновляем конфигурацию API с введенными данными
      API_CONFIG.AUTH_CREDENTIALS.username = email;
      API_CONFIG.AUTH_CREDENTIALS.password = password;

      // Пытаемся получить токен
      const token = await authService.getAccessToken();

      if (token) {
        // Получаем информацию о пользователе
        const userInfo = await fetchUserInfo(email);

        // Успешная авторизация
        const userData = {
          email,
          userId: userInfo?.UserId || null,
          lastName: userInfo?.LastName || null,
          firstName: userInfo?.FirstName || null,
          middleName: userInfo?.MiddleName || null,
          position: userInfo?.Position || null,
          phone: userInfo?.Phone || null,
          companyName: userInfo?.CompanyName || null,
          companyId: userInfo?.CompanyId || null,
          regionId: userInfo?.RegionId || null,
          description: userInfo?.Description || null,
          bin: userInfo?.Bin || null,
          kbe: userInfo?.KBE || null,
          bankName: userInfo?.BankName || null,
          bik: userInfo?.BIK || null,
          iik: userInfo?.IIK || null,
          legalAddress: userInfo?.LegalAddress || null,
          actualAddress: userInfo?.ActualAddress || null,
          site: userInfo?.Site || null,
          fax: userInfo?.Fax || null,
          uTypeId: userInfo?.UTypeId || null,
          cTypeId: userInfo?.CTypeId || null,
          businessScopeId: userInfo?.BusinessScopeId || null,
        };
        setUser(userData);
        setIsAuthenticated(true);
        setAuthError("");

        // Сохраняем токен в зависимости от "Запомнить меня"
        if (typeof window !== "undefined") {
          if (rememberMe) {
            // Сохраняем в localStorage для постоянного хранения
            localStorage.setItem("api_access_token", token);
            localStorage.setItem("user_data", JSON.stringify(userData));
            AuthDebug.logTokenSaved(token, "localStorage");
            console.log(
              "💾 Токен и данные пользователя сохранены в localStorage (постоянно)"
            );
          } else {
            // Сохраняем в sessionStorage только на время сессии
            sessionStorage.setItem("api_access_token", token);
            sessionStorage.setItem("user_data", JSON.stringify(userData));
            AuthDebug.logTokenSaved(token, "sessionStorage");
            console.log(
              "💾 Токен и данные пользователя сохранены в sessionStorage (до закрытия браузера)"
            );
          }
        }

        console.log("✅ Успешная авторизация для:", email);
        return true;
      } else {
        // Ошибка получения токена
        AuthDebug.logAuthError(
          new Error("Токен не получен"),
          "login - токен отсутствует"
        );
        setAuthError("Неверный email или пароль");
        return false;
      }
    } catch (error) {
      AuthDebug.logAuthError(error, "login - общая ошибка");

      // Пытаемся извлечь детальную информацию об ошибке
      let errorMessage = "Неверный email или пароль";

      if (error.message) {
        // Проверяем, содержит ли ошибка JSON ответ от сервера
        try {
          const errorData = JSON.parse(error.message);
          if (errorData.error_description) {
            errorMessage = translateApiError(errorData.error_description);
          } else if (errorData.error) {
            errorMessage = translateApiError(errorData.error);
          }
        } catch (parseError) {
          // Если не JSON, переводим как обычное сообщение
          errorMessage = translateApiError(error.message);
        }
      }

      setAuthError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Функция регистрации
  const register = async (
    email,
    password,
    confirmPassword,
    rememberMe = false
  ) => {
    setIsLoading(true);
    setAuthError("");

    try {
      // Проверяем, что пароли совпадают
      if (password !== confirmPassword) {
        setAuthError("Пароли не совпадают");
        return false;
      }

      // Отправляем запрос на регистрацию
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/Account/Register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            Email: email,
            Password: password,
            ConfirmPassword: confirmPassword,
          }),
        }
      );

      const data = await response.json();

      if (response.ok && data.Succeeded) {
        console.log("Регистрация успешна, получаем токен и UserId...");

        // Обновляем конфигурацию API с данными нового пользователя
        API_CONFIG.AUTH_CREDENTIALS.username = email;
        API_CONFIG.AUTH_CREDENTIALS.password = password;

        // Получаем токен для нового пользователя
        const token = await authService.getAccessToken();

        if (token) {
          // Получаем базовую информацию о пользователе через /api/Account/UserInfo
          const accountUserInfo = await fetchAccountUserInfo(token);

          if (accountUserInfo) {
            // Создаем базовые данные пользователя с UserId
            const userData = {
              email,
              userId: accountUserInfo.UserId,
              lastName: null,
              firstName: null,
              middleName: null,
              position: null,
              phone: null,
              companyName: null,
              companyId: null,
              regionId: null,
              description: null,
              bin: null,
              kbe: null,
              bankName: null,
              bik: null,
              iik: null,
              legalAddress: null,
              actualAddress: null,
              site: null,
              fax: null,
              uTypeId: null,
              cTypeId: null,
              businessScopeId: null,
              // Сохраняем флаг, что пользователь только зарегистрировался
              isNewUser: true,
            };

            setUser(userData);
            setIsAuthenticated(true);
            setAuthError("");

            // Сохраняем токен и данные в зависимости от "Запомнить меня"
            if (typeof window !== "undefined") {
              if (rememberMe) {
                localStorage.setItem("api_access_token", token);
                localStorage.setItem("user_data", JSON.stringify(userData));
                console.log(
                  "💾 Токен и данные нового пользователя сохранены в localStorage"
                );
              } else {
                sessionStorage.setItem("api_access_token", token);
                sessionStorage.setItem("user_data", JSON.stringify(userData));
                console.log(
                  "💾 Токен и данные нового пользователя сохранены в sessionStorage"
                );
              }
            }

            console.log(
              "✅ Регистрация завершена успешно, UserId:",
              accountUserInfo.UserId
            );
            return true;
          } else {
            setAuthError("Не удалось получить информацию о пользователе");
            return false;
          }
        } else {
          setAuthError("Не удалось получить токен доступа");
          return false;
        }
      } else {
        // Обрабатываем ошибки регистрации
        let errorMessage = "Ошибка при регистрации";

        // Проверяем специфичные ошибки API
        if (data.ModelState) {
          const modelStateErrors = [];
          Object.values(data.ModelState).forEach((errors) => {
            if (Array.isArray(errors)) {
              modelStateErrors.push(...errors);
            }
          });

          // Переводим все ошибки на русский язык
          const translatedErrors = modelStateErrors.map((error) =>
            translateApiError(error)
          );

          // Проверяем, есть ли ошибки о том, что пользователь уже существует
          const alreadyExistsErrors = modelStateErrors.filter(
            (error) =>
              error.includes("is already taken") ||
              error.includes("уже существует") ||
              error.includes("уже зарегистрирован")
          );

          if (alreadyExistsErrors.length > 0) {
            errorMessage =
              "Пользователь с таким email уже зарегистрирован. Попробуйте войти в систему.";
          } else {
            errorMessage = translatedErrors.join(" ");
          }
        } else if (data.Errors && data.Errors.length > 0) {
          const translatedErrors = data.Errors.map((error) =>
            translateApiError(error)
          );
          errorMessage = translatedErrors.join(" ");
        } else if (data.Message) {
          errorMessage = translateApiError(data.Message);
        }

        setAuthError(errorMessage);
        return false;
      }
    } catch (error) {
      console.error("Ошибка регистрации:", error);
      setAuthError("Ошибка при регистрации. Попробуйте еще раз.");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Функция для обновления данных пользователя из API
  const refreshUserData = async () => {
    if (!user?.email) {
      console.warn("⚠️ Нет email пользователя для обновления данных");
      return false;
    }

    try {
      console.log("🔄 Обновление данных пользователя из API...");
      const userInfo = await fetchUserInfo(user.email);

      if (userInfo) {
        // Обновляем данные пользователя
        const updatedUserData = {
          email: user.email,
          userId: userInfo?.UserId || user.userId,
          lastName: userInfo?.LastName || null,
          firstName: userInfo?.FirstName || null,
          middleName: userInfo?.MiddleName || null,
          position: userInfo?.Position || null,
          phone: userInfo?.Phone || null,
          companyName: userInfo?.CompanyName || null,
          companyId: userInfo?.CompanyId || null,
          regionId: userInfo?.RegionId || null,
          description: userInfo?.Description || null,
          bin: userInfo?.Bin || null,
          kbe: userInfo?.KBE || null,
          bankName: userInfo?.BankName || null,
          bik: userInfo?.BIK || null,
          iik: userInfo?.IIK || null,
          legalAddress: userInfo?.LegalAddress || null,
          actualAddress: userInfo?.ActualAddress || null,
          site: userInfo?.Site || null,
          fax: userInfo?.Fax || null,
          uTypeId: userInfo?.UTypeId || null,
          cTypeId: userInfo?.CTypeId || null,
          businessScopeId: userInfo?.BusinessScopeId || null,
          isNewUser: false,
        };

        setUser(updatedUserData);

        // Обновляем данные в хранилище
        const rememberMe = localStorage.getItem("rememberMe") === "true";
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem("userData", JSON.stringify(updatedUserData));

        console.log("✅ Данные пользователя успешно обновлены");
        return true;
      } else {
        console.warn("⚠️ Не удалось получить обновленные данные пользователя");
        return false;
      }
    } catch (error) {
      console.error("❌ Ошибка при обновлении данных пользователя:", error);
      return false;
    }
  };

  // Функция для создания профиля пользователя в /api/PUsers
  const createUserProfile = async (profileData) => {
    try {
      // Получаем текущую дату и время
      const currentDateTime = new Date().toISOString();

      // Подготавливаем данные для отправки
      const requestData = {
        UserId: user?.userId || profileData.userId,
        UTypeId: profileData.legalForm === "individual" ? 1 : 2,
        CompanyId: 0, // Формируется автоматически
        LastName: profileData.lastName || "",
        FirstName: profileData.firstName || "",
        MiddleName: profileData.middleName || "",
        Position: profileData.position || "",
        Phone: profileData.phone || "",
        Email: profileData.email || user?.email,
        IsAgreement: true,
        RegDate: currentDateTime,
        CreatedBy: user?.userId || profileData.userId,
        CreationTime: currentDateTime,
        ModifiedBy: user?.userId || profileData.userId,
        ModifiedTime: currentDateTime,
        CTypeId: 0, // Пока пустое
        CompanyName: profileData.companyName || "",
        LegalAddress: profileData.legalAddress || "",
        ActualAddress: profileData.actualAddress || "",
        Site: "", // Пока пустое
        CountryId: 113, // Константа для Казахстана
        RegionId: profileData.cityId || "",
        Fax: "", // Пока пустое
        Description: profileData.companyStory || "",
        BusinessScopeId:
          profileData.activities && profileData.activities.length > 0
            ? profileData.activities[0].id
            : 0,
        Bin: profileData.bin || "",
        logo: "", // Пока пустое
        KBE: profileData.kbe || "",
        IIK: profileData.iik || "",
        BankName: profileData.bankName || "",
        BIK: profileData.bik || "",
      };

      console.log("📤 Отправляем данные профиля:", requestData);

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/PUsers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authService.currentToken}`,
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Профиль пользователя создан успешно:", result);

        // Обновляем данные пользователя в контексте
        const updatedUserData = {
          ...user,
          lastName: profileData.lastName,
          firstName: profileData.firstName,
          middleName: profileData.middleName,
          position: profileData.position,
          phone: profileData.phone,
          companyName: profileData.companyName,
          companyId: result.CompanyId || 0,
          regionId: profileData.cityId,
          description: profileData.companyStory,
          bin: profileData.bin,
          kbe: profileData.kbe,
          bankName: profileData.bankName,
          bik: profileData.bik,
          iik: profileData.iik,
          legalAddress: profileData.legalAddress,
          actualAddress: profileData.actualAddress,
          site: profileData.site,
          fax: profileData.fax,
          uTypeId: profileData.legalForm === "individual" ? 1 : 2,
          cTypeId: result.CTypeId || 0,
          businessScopeId:
            profileData.activities && profileData.activities.length > 0
              ? profileData.activities[0].id
              : 0,
          isNewUser: false, // Убираем флаг нового пользователя
        };

        setUser(updatedUserData);

        // Обновляем данные в хранилище
        if (typeof window !== "undefined") {
          if (localStorage.getItem("api_access_token")) {
            localStorage.setItem("user_data", JSON.stringify(updatedUserData));
          } else if (sessionStorage.getItem("api_access_token")) {
            sessionStorage.setItem(
              "user_data",
              JSON.stringify(updatedUserData)
            );
          }
        }

        return { success: true, data: result };
      } else {
        const errorData = await response.json();
        console.error("❌ Ошибка при создании профиля:", errorData);
        return {
          success: false,
          error:
            errorData.message || "Ошибка при создании профиля пользователя",
        };
      }
    } catch (error) {
      console.error("❌ Ошибка при отправке данных профиля:", error);
      return {
        success: false,
        error: "Ошибка при создании профиля пользователя",
      };
    }
  };

  // Функция выхода из системы
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    setAuthError("");
    authService.clearToken();

    // Очищаем данные из обоих хранилищ
    if (typeof window !== "undefined") {
      localStorage.removeItem("api_access_token");
      localStorage.removeItem("user_data");
      sessionStorage.removeItem("api_access_token");
      sessionStorage.removeItem("user_data");
      AuthDebug.logTokenCleared();
    }

    // Очищаем данные из конфигурации
    API_CONFIG.AUTH_CREDENTIALS.username = "";
    API_CONFIG.AUTH_CREDENTIALS.password = "";

    console.log("🚪 Пользователь вышел из системы");
  };

  // Очистка ошибки
  const clearError = () => {
    setAuthError("");
  };

  // Значение контекста
  const value = {
    user,
    isAuthenticated,
    authError,
    isLoading,
    login,
    register,
    logout,
    clearError,
    createUserProfile,
    refreshUserData,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
