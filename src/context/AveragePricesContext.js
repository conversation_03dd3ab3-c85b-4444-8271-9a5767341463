"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import ApiService from "../services/api.service";
import API_CONFIG from "../config/api.js";

const AveragePricesContext = createContext();

/**
 * Провайдер контекста для управления средними ценами
 */
export const AveragePricesProvider = ({ children }) => {
  const [currentMaterialIds, setCurrentMaterialIds] = useState([]);

  // Получаем средние цены для текущих материалов
  const {
    data: averagePrices = [],
    isLoading: pricesLoading,
    error: pricesError,
    refetch: refetchPrices,
  } = useQuery({
    queryKey: ["averagePrices", currentMaterialIds?.sort()?.join(",")],
    queryFn: () => ApiService.getAveragePrices(currentMaterialIds),
    // Не выполнять запрос, если нет materialIds
    enabled: !!currentMaterialIds && currentMaterialIds.length > 0,
    // Кэшируем данные на 5 минут
    staleTime: 5 * 60 * 1000,
  });

  // Получаем файлы для текущих материалов (новый API)
  const {
    data: materialFiles = {},
    isLoading: filesLoading,
    error: filesError,
    refetch: refetchFiles,
  } = useQuery({
    queryKey: ["materialFiles", currentMaterialIds?.sort()?.join(",")],
    queryFn: () => ApiService.getMaterialFiles(currentMaterialIds),
    // Не выполнять запрос, если нет materialIds
    enabled: !!currentMaterialIds && currentMaterialIds.length > 0,
    // Кэшируем данные на 10 минут (фотографии меняются реже цен)
    staleTime: 10 * 60 * 1000,
  });

  // Функция для обновления списка ID материалов
  const updateMaterialIds = useCallback((materialIds) => {
    setCurrentMaterialIds(materialIds);
  }, []);

  // Функция для получения цены по ID материала
  const getPriceByMaterialId = useCallback(
    (materialId) => {
      const priceData = averagePrices.find(
        (item) => item.MaterialId === materialId
      );
      return priceData || null;
    },
    [averagePrices]
  );

  // Функция для получения файлов по ID материала (новый API)
  const getFilesByMaterialId = useCallback(
    (materialId) => {
      const files = materialFiles[materialId?.toString()];
      return files || [];
    },
    [materialFiles]
  );

  // Функция для формирования URL файла по CertificateId
  const getFileUrl = useCallback((certificateId) => {
    if (!certificateId) {
      return "/images/placeholder.png";
    }
    return `${API_CONFIG.BASE_URL}/api/Adverts/File?certificateId=${certificateId}`;
  }, []);

  // Функция для получения URL изображений по MaterialId (новый API)
  const getImageUrlsByMaterialId = useCallback(
    (materialId) => {
      const files = getFilesByMaterialId(materialId);
      if (!files || files.length === 0) {
        return ["/images/placeholder.png"];
      }
      return files.map((file) => getFileUrl(file.CertificateId));
    },
    [getFilesByMaterialId, getFileUrl]
  );

  // Функция для получения первого URL изображения по MaterialId (новый API)
  const getFirstImageUrlByMaterialId = useCallback(
    (materialId) => {
      const urls = getImageUrlsByMaterialId(materialId);
      return urls[0] || "/images/placeholder.png";
    },
    [getImageUrlsByMaterialId]
  );

  // Функция для получения цен и фотографий для массива продуктов
  const updatePricesForProducts = useCallback(
    (products) => {
      if (!products || products.length === 0) {
        updateMaterialIds([]);
        return;
      }

      const materialIds = products
        .map((product) => product.MaterialId)
        .filter(Boolean);

      updateMaterialIds(materialIds);
    },
    [updateMaterialIds]
  );

  const value = {
    // Данные цен
    averagePrices,
    currentMaterialIds,

    // Данные файлов (новый API)
    materialFiles,

    // Состояние загрузки
    isLoading: pricesLoading || filesLoading,
    pricesLoading,
    filesLoading,
    error: pricesError || filesError,
    pricesError,
    filesError,

    // Функции для цен
    updateMaterialIds,
    updatePricesForProducts,
    getPriceByMaterialId,
    refetch: () => {
      refetchPrices();
      refetchFiles();
    },
    refetchPrices,
    refetchFiles,

    // Функции для файлов (новый API)
    getFilesByMaterialId,
    getFileUrl,
    getImageUrlsByMaterialId,
    getFirstImageUrlByMaterialId,
  };

  return (
    <AveragePricesContext.Provider value={value}>
      {children}
    </AveragePricesContext.Provider>
  );
};

/**
 * Хук для использования контекста средних цен
 */
export const useAveragePricesContext = () => {
  const context = useContext(AveragePricesContext);
  if (!context) {
    throw new Error(
      "useAveragePricesContext must be used within AveragePricesProvider"
    );
  }
  return context;
};
