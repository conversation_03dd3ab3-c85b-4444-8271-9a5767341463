"use client";

import React, { createContext, useState, useContext, useEffect } from "react";

// Создаем контекст для корзины
const CartContext = createContext();

// Хук для использования контекста корзины
export const useCart = () => {
  return useContext(CartContext);
};

// Провайдер контекста корзины
export const CartProvider = ({ children }) => {
  // Загружаем данные корзины из localStorage при инициализации
  const [cartItems, setCartItems] = useState(() => {
    if (typeof window !== "undefined") {
      const savedCart = localStorage.getItem("cart");
      return savedCart ? JSON.parse(savedCart) : [];
    }
    return [];
  });

  // Сохраняем данные корзины в localStorage при изменении
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("cart", JSON.stringify(cartItems));
    }
  }, [cartItems]);

  // Функция добавления товара в корзину
  const addToCart = (product, quantity = 1) => {
    setCartItems((prevItems) => {
      // Используем id для поиска существующего товара
      const existingItem = prevItems.find((item) => item.id === product.id);

      if (existingItem) {
        // Если товар уже есть в корзине, увеличиваем количество
        return prevItems.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Если товара нет в корзине, добавляем его
        return [...prevItems, { ...product, quantity }];
      }
    });
  };

  // Функция удаления товара из корзины
  const removeFromCart = (productId) => {
    setCartItems((prevItems) =>
      prevItems.filter((item) => item.id !== productId)
    );
  };

  // Функция обновления количества товара в корзине
  const updateQuantity = (productId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.id === productId ? { ...item, quantity } : item
      )
    );
  };

  // Функция временного обновления количества (для редактирования)
  const updateQuantityTemp = (productId, quantity) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.id === productId ? { ...item, quantity } : item
      )
    );
  };

  // Функция очистки корзины
  const clearCart = () => {
    setCartItems([]);
  };

  // Функция получения количества уникальных товаров в корзине
  const getCartItemsCount = () => {
    return cartItems.length;
  };

  // Функция получения общей стоимости корзины
  const getCartTotal = () => {
    return cartItems.reduce((total, item) => {
      const price = item.Price || 0;
      return total + price * item.quantity;
    }, 0);
  };

  // Функция проверки наличия товара в корзине
  const isInCart = (productId) => {
    return cartItems.some((item) => item.id === productId);
  };

  // Функция получения количества товара в корзине
  const getItemQuantity = (productId) => {
    const item = cartItems.find((item) => item.id === productId);
    return item ? item.quantity : 0;
  };

  // Значение контекста
  const value = {
    cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    updateQuantityTemp,
    clearCart,
    getCartItemsCount,
    getCartTotal,
    isInCart,
    getItemQuantity,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};
