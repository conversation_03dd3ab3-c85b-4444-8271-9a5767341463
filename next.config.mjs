/** @type {import('next').NextConfig} */
const nextConfig = {
  compiler: {
    styledComponents: {
      ssr: true,
      displayName: true,
    },
  },

  // Оптимизация для production
  swcMinify: true,

  // Отключаем ESLint для быстрого билда
  eslint: {
    ignoreDuringBuilds: true,
  },

  // SEO и производительность
  poweredByHeader: false, // Убираем X-Powered-By заголовок
  compress: true, // Включаем gzip сжатие

  // Оптимизация изображений
  images: {
    formats: ["image/webp", "image/avif"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 дней кэш
  },

  // Заголовки безопасности и SEO
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()",
          },
        ],
      },
      {
        source: "/sitemap.xml",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=3600, s-maxage=3600",
          },
        ],
      },
      {
        source: "/robots.txt",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400, s-maxage=86400",
          },
        ],
      },
    ];
  },

  // Редиректы для SEO
  async redirects() {
    return [
      {
        source: "/products",
        destination: "/products/page/1",
        permanent: true,
      },
      {
        source: "/catalog",
        destination: "/products/page/1",
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
