# Оптимизация для Vercel: ISR + Экономия Invocations

## Проблема

- ~100,000 товаров на сайте
- SSR генерирует >1000 invocations в день
- Googlebot сканирует все sitemap → перерасход лимитов Vercel
- Нужно сохранить SEO при снижении нагрузки

## Решение: ISR + Ограничения

### 1. ISR (Incremental Static Regeneration)

**Страницы товаров (`/product/[id]`):**

- ✅ `generateStaticParams()` - предгенерация 100 популярных товаров
- ✅ `revalidate = 7 дней` - обновление раз в неделю
- ✅ Остальные товары генерируются по требованию (fallback)

**Конфигурация:**

```javascript
// src/config/isr.js
export const ISR_CONFIG = {
  REVALIDATE_TIMES: {
    PRODUCT_POPULAR: 24 * 60 * 60, // 1 день
    PRODUCT_REGULAR: 7 * 24 * 60 * 60, // 7 дней
  },
  STATIC_GENERATION_LIMITS: {
    PRODUCTS_PREBUILD: 100, // Только 100 товаров при билде
  },
};
```

### 2. Оптимизация Robots.txt

**Ограничения для ботов:**

- ✅ Googlebot: доступ до страницы 2000
- ✅ YandexBot: доступ до страницы 1000
- ✅ Другие боты: доступ до страницы 1000
- ✅ Увеличенные crawlDelay (1-3 секунды)

**Результат:** Боты сканируют меньше страниц = меньше invocations

### 3. Агрессивная оптимизация Sitemap

**Ограничения размеров (обновлено):**

- ✅ sitemap-products.xml: максимум 1,000 товаров (вместо 100,000)
- ✅ sitemap-pages.xml: максимум 500 страниц каталога
- ✅ Основной sitemap.xml: только 5 самых важных страниц

**Приоритеты:**

- Популярные товары (первые 1000): priority 0.7
- Обычные товары: priority 0.5
- Страницы каталога: priority 0.4

### 4. Кэширование

**Middleware (`src/middleware.js`):**

- ✅ Sitemap кэшируется на 1 час
- ✅ Страницы товаров кэшируются на 1 день (браузер) / 7 дней (CDN)
- ✅ Оптимизированные заголовки

### 5. Мониторинг

**Логирование ISR операций:**

```javascript
ISRUtils.logISROperation("generateStaticParams", {
  type: "products",
  generated: products.length,
  limit: 100,
});
```

## Ожидаемые результаты

### До оптимизации:

- 🔴 1000+ invocations/день
- 🔴 Все 100,000 товаров в sitemap
- 🔴 SSR для каждого запроса бота

### После агрессивной оптимизации:

- 🟢 ~50-100 invocations/день (еще меньше!)
- 🟢 Только 1,000 товаров в sitemap
- 🟢 ISR: revalidate 30 дней + кэширование
- 🟢 Строгие ограничения ботов
- 🟢 SEO-ссылки для компенсации

## Сохранение SEO

✅ **Индексация не пострадает:**

- Первые 2,500 товаров в sitemap (самые важные)
- Остальные товары доступны по прямым ссылкам
- ISR обеспечивает актуальность контента
- Структурированные данные сохранены

✅ **Производительность улучшится:**

- Статические страницы загружаются быстрее
- CDN кэширование работает эффективнее
- Меньше нагрузки на API

## Деплой

1. **Проверьте конфигурацию:**

   ```bash
   npm run build
   ```

2. **Деплой на Vercel:**

   ```bash
   vercel --prod
   ```

3. **Мониторинг:**
   - Vercel Dashboard → Functions → Invocations
   - Логи ISR операций в консоли

## Дополнительные оптимизации

### Если invocations все еще высокие:

1. **Увеличить revalidate время:**

   ```javascript
   export const revalidate = 14 * 24 * 60 * 60; // 14 дней
   ```

2. **Уменьшить sitemap:**

   ```javascript
   const maxProducts = 1000; // Вместо 2500
   ```

3. **Более строгие robots.txt:**
   ```
   Disallow: /products/page/[5-9][0-9][0-9]*  # Страницы 500+
   ```

## Проверка результатов

1. **Vercel Dashboard:**

   - Functions → Invocations (должно быть <200/день)
   - Bandwidth (должен снизиться)

2. **Google Search Console:**

   - Coverage → Valid pages (должны остаться)
   - Sitemaps → Submitted URLs (будет меньше, но это нормально)

3. **Производительность:**
   - PageSpeed Insights
   - Core Web Vitals

## Откат (если что-то пошло не так)

1. Удалить `generateStaticParams` из `/product/[id]/page.js`
2. Убрать `revalidate` экспорт
3. Вернуть старые sitemap без ограничений
4. Упростить robots.txt
