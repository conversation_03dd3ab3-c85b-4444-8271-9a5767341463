# 📸 Примеры компонентов многошаговой регистрации

## 🎨 Кнопка "Назад"

```jsx
<BackButton onClick={onBack}>
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z" />
  </svg>
  Назад
</BackButton>
```

**Стили:**
- Фон: `#f8f9fa` (светло-серый)
- Цвет текста: `#0066cc` (синий)
- Граница: `#e9ecef`
- Hover: граница становится синей `#0066cc`
- Иконка: SVG стрелка влево 20x20px

## 📊 Прогресс-бар

```jsx
<ProgressContainer>
  <ProgressText>
    Подробная информация о компании, увеличивает шансы на заключение сделки именно с вами.
  </ProgressText>
  <ProgressBarContainer>
    <ProgressBar>
      <ProgressFill percentage={45} />
    </ProgressBar>
    <ProgressPercentage>45%</ProgressPercentage>
  </ProgressBarContainer>
</ProgressContainer>
```

**Особенности:**
- Динамический расчет процентов: `(currentStep / totalSteps) * 100`
- Зеленый цвет заливки: `#28a745`
- Анимация изменения ширины: `transition: width 0.3s ease`

## 🔘 Радио-кнопки

```jsx
<RadioOption 
  selected={formData.legalForm === 'individual'}
  onClick={() => updateFormData({ legalForm: 'individual' })}
>
  <RadioLabel>Я физическое лицо</RadioLabel>
  <RadioButton selected={formData.legalForm === 'individual'} />
</RadioOption>
```

**Стили:**
- Неактивная: белый фон, серая граница
- Активная: `#f0f8ff` фон, синяя граница `#0066cc`
- Чекбокс: квадратный с галочкой ✓

## 📝 Поля ввода

```jsx
<FormGroup>
  <Label>Имя*</Label>
  <Input
    type="text"
    placeholder="Руслан"
    value={formData.firstName || ''}
    onChange={(e) => updateFormData({ firstName: e.target.value })}
  />
</FormGroup>
```

**Эффекты:**
- Фокус: синяя граница + тень `box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1)`
- Фон меняется с `#f8f9fa` на белый при фокусе

## 🏷️ Теги деятельности

```jsx
<ActivityOption 
  selected={formData.activities?.includes(activity)}
  onClick={() => toggleActivity(activity)}
>
  <ActivityLabel>Электрика</ActivityLabel>
  <ActivityCheckbox selected={formData.activities?.includes(activity)} />
</ActivityOption>
```

**Логика:**
- Множественный выбор через массив
- Переключение через `includes()` и `filter()`

## 📁 Загрузка файлов

```jsx
<ImageUploadArea>
  <UploadIcon>📁</UploadIcon>
  <UploadText>
    Перетащите ваши картинки сюда или нажмите, чтобы выбрать и загрузить файлы обычным способом.
  </UploadText>
</ImageUploadArea>
```

**Дизайн:**
- Пунктирная граница: `border: 2px dashed #e9ecef`
- Hover: синяя граница + голубой фон
- Иконка: эмодзи 📁 размером 32px

## 🎯 Навигация между шагами

```jsx
// Логика определения количества шагов
const getTotalSteps = () => {
  return formData.legalForm === "legal" ? 9 : 4;
};

// Валидация для перехода
const canProceed = () => {
  switch (currentStep) {
    case 1: return formData.legalForm !== "";
    case 2: return formData.firstName && formData.lastName && formData.phone && formData.email;
    case 3: return formData.city !== "";
    default: return true;
  }
};
```

## 📱 Адаптивность

```css
@media (max-width: 768px) {
  padding: 24px;
  border-radius: 8px;
  max-width: 100%;
}

@media (max-width: 768px) {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}
```

## 🔄 Управление состоянием

```jsx
const [formData, setFormData] = useState({
  email: initialEmail || "",
  legalForm: "",
  firstName: "",
  lastName: "",
  // ... остальные поля
});

const updateFormData = (newData) => {
  setFormData(prev => ({ ...prev, ...newData }));
};
```

## 🎨 Цветовая схема

- **Основной синий**: `#0066cc`
- **Синий hover**: `#0055b3` 
- **Зеленый прогресс**: `#28a745`
- **Светло-серый фон**: `#f8f9fa`
- **Границы**: `#e9ecef`
- **Текст**: `#333` (основной), `#666` (вторичный)
- **Placeholder**: `#adb5bd`

## 🚀 Интеграция

```jsx
// В компоненте страницы
<RegistrationWizard
  initialEmail={formData.email}
  onComplete={handleRegistrationComplete}
  onCancel={handleRegistrationCancel}
/>

// Обработчики
const handleRegistrationComplete = (profileData) => {
  console.log("Профиль заполнен:", profileData);
  // API запрос для сохранения
};
```

Все компоненты созданы с использованием styled-components и следуют единой дизайн-системе проекта!
