# Анализ Vercel Invocations: До и После Оптимизации

## 📊 Источники invocations

### ❌ **ДО оптимизации (1000+ invocations/день):**

| Источник | Количество | Причина |
|----------|------------|---------|
| **Товарные страницы** | ~800/день | 100,000 товаров в sitemap → боты сканируют массово |
| **Каталог страницы** | ~150/день | 5,000+ страниц в sitemap |
| **Категории** | ~50/день | Все категории в sitemap |
| **Пользователи** | ~50/день | Обычный трафик |
| **ИТОГО** | **1050+/день** | 🔴 Превышение лимитов |

### ✅ **ПОСЛЕ оптимизации (~50-100 invocations/день):**

| Источник | Количество | Оптимизация |
|----------|------------|-------------|
| **Товарные страницы (ISR)** | ~20/день | 1,000 товаров в sitemap + ISR кэш 30 дней |
| **Каталог страницы (ISR)** | ~15/день | 500 страниц в sitemap + ISR кэш 6 часов |
| **Категории (SSR)** | ~20/день | Ограничения robots.txt |
| **Пользователи** | ~30/день | Обычный трафик |
| **ИТОГО** | **~85/день** | 🟢 В пределах лимитов |

## 🔍 **Почему SSR страницы безопасны:**

### **1. Каталог (`/products/page/[page]`):**
- **Sitemap ограничен:** 500 страниц (было 5,000+)
- **Robots.txt:** боты не могут сканировать страницы 500+
- **ISR добавлен:** первые 10 страниц кэшируются на 6 часов
- **Реальность:** пользователи редко идут дальше 5-й страницы

### **2. Категории (`/products/category/[category]/page/[page]`):**
- **Sitemap ограничен:** только первые 3 страницы каждой категории
- **Robots.txt:** те же ограничения
- **Поведение ботов:** сканируют умеренно

## 📈 **Математика экономии:**

```
До:  100,000 товаров × 1% сканирования в день = 1,000 invocations
После: 1,000 товаров × 2% сканирования в день = 20 invocations

Экономия: 98% invocations от товаров!
```

## 🛡️ **Дополнительные защиты:**

### **Robots.txt ограничения:**
```
# Общие боты
Disallow: /products/page/[5-9][0-9][0-9]*  # Страницы 500+
Disallow: /product/[5-9][0-9][0-9][0-9][0-9]*  # Товары 50000+

# Google (более мягкие ограничения)
Disallow: /products/page/[1-9][0-9][0-9][0-9]*  # Страницы 1000+
Disallow: /product/[1-9][0-9][0-9][0-9][0-9]*  # Товары 10000+
```

### **ISR кэширование:**
- **Товары:** 30 дней (2,592,000 секунд)
- **Каталог:** 6 часов (21,600 секунд)
- **Результат:** повторные запросы не генерируют invocations

## 🎯 **Мониторинг результатов:**

### **Vercel Dashboard проверки:**
1. **Functions → Invocations:** должно быть <100/день
2. **Bandwidth:** должен снизиться
3. **Build time:** может увеличиться (ISR генерация)

### **Google Search Console:**
1. **Coverage:** индексированные страницы не должны сильно упасть
2. **Sitemaps:** меньше URL, но это нормально
3. **Performance:** может улучшиться (быстрее загрузка)

## 🚨 **Если invocations все еще высокие:**

### **Уровень 1 (текущий):**
- 1,000 товаров в sitemap
- 500 страниц каталога
- ISR 30 дней для товаров

### **Уровень 2 (более агрессивный):**
```javascript
// В sitemap-products.xml
const maxProducts = 500; // Вместо 1000

// В sitemap-pages.xml  
const maxPages = 200; // Вместо 500

// В product/[id]/page.js
export const revalidate = 2592000 * 2; // 60 дней вместо 30
```

### **Уровень 3 (экстремальный):**
```javascript
// Только 100 товаров в sitemap
const maxProducts = 100;

// Только 50 страниц каталога
const maxPages = 50;

// ISR на 90 дней
export const revalidate = 2592000 * 3;
```

## ✅ **Заключение:**

**SSR страницы НЕ перегружают invocations** потому что:
1. Ограничены в sitemap (500 вместо 5000+)
2. Ограничены в robots.txt
3. Первые 10 страниц используют ISR
4. Боты сканируют их умеренно

**Основная экономия достигнута** за счет:
- ISR для товарных страниц (98% экономии)
- Уменьшения sitemap в 100 раз
- Строгих ограничений robots.txt

**Ожидаемый результат: 85 invocations/день** 🎯
